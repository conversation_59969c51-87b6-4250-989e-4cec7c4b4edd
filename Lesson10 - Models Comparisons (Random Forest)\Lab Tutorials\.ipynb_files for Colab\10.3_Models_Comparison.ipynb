{"cells": [{"cell_type": "markdown", "id": "6aa6001a", "metadata": {}, "source": ["# Classificatiom Model 3: Random Forest\n", "\n", "***\n", "Let's develop our random forest model for the laundromat use case. \n"]}, {"cell_type": "code", "execution_count": null, "id": "5f3e7d88", "metadata": {}, "outputs": [], "source": ["from google.colab import drive\n", "\n", "drive.mount(??, force_remount=True)"]}, {"cell_type": "code", "execution_count": null, "id": "f26c592f", "metadata": {}, "outputs": [], "source": ["ROOT_DIR = ??"]}, {"cell_type": "code", "execution_count": 11, "id": "258c8425", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["     index_col       avC         avP       sdC         sdP          avR  maxC  \\\n", "432          3  3.595600  792.930000  3.332669  786.913383   399.232039  7.23   \n", "22           9  1.616815  329.370370  2.485054  583.668067   637.157143  1.76   \n", "620         14  1.305465  254.702326  2.232888  529.072646   604.123431  0.66   \n", "963          0  0.284667   34.533333  0.227744   33.000244  1429.358887  0.77   \n", "188          9  1.484593  298.003704  2.357156  558.706609   623.840883  1.84   \n", "..         ...       ...         ...       ...         ...          ...   ...   \n", "360         21  0.906095  167.071429  1.721403  405.103331   871.333092  0.09   \n", "466         15  1.292000  256.215556  2.277856  534.821437   658.162380  1.30   \n", "299          0  2.612000  574.333333  3.216506  759.213038   753.064179  7.25   \n", "493         20  1.080317  202.135000  1.919027  450.266332   678.029294  2.49   \n", "527          9  1.641481  339.711111  2.584596  619.005346   585.429776  0.59   \n", "\n", "     maxP     stdCR       stdCP         AvRR  \n", "432  1642  3.382349  805.313577   362.408577  \n", "22    271  0.587681   92.810374   449.344412  \n", "620    69  0.141624   18.216104   682.374211  \n", "963   102  0.227744   33.000244  1429.358887  \n", "188   286  0.601370   96.551553   475.380790  \n", "..    ...       ...         ...          ...  \n", "360     1  0.000000    0.000000  2628.307407  \n", "466   195  0.325882   50.023153   638.702682  \n", "299  1666  3.216506  759.213038   753.064179  \n", "493   395  0.630406  102.162728   226.295845  \n", "527    75  0.109605   15.296908   559.013457  \n", "\n", "[773 rows x 11 columns]\n", "432     CottonWash\n", "22      DailyRinse\n", "620    CottonRinse\n", "963    BeddingWash\n", "188     DailyRinse\n", "          ...     \n", "360     CottonSpin\n", "466    CottonRinse\n", "299     CottonWash\n", "493     CottonSpin\n", "527     CottonWash\n", "Name: mode, Length: 773, dtype: object\n"]}], "source": ["# Import necessary Python libraries here ...\n", "# ...\n", "# ...\n", "# ...\n", "\n", "# Loading the dataset from the /data folder here \n", "data_path  = ?\n", "\n", "# Read your csv file here ...\n", "currentdf  = ???\n", "\n", "# Allocate your training data and label \n", "x = ?\n", "y = ?\n", "\n", "# Splitting dataset into 75% for training and 25% for testing here ...\n", "# ...\n", "# ...\n", "\n", "# Display the features and label from the training set \n", "print(?)\n", "print(?)\n", "\n", "# Insert code to standardize your dataset here ...\n", "# ...\n", "# ...\n"]}, {"cell_type": "markdown", "id": "f2dc0885", "metadata": {}, "source": ["***\n", "## Training vs Testing Error\n", "\n", "As you have learnt 3 very useful classifiers, it is time to evaluate their accuracy on the testing set. In the training set, the 3 models appear to give us about the same arracy of around 80% - 90% range. However, we need a reference point to determine whether this 80% is good or bad.\n", "\n", "We can compare the random forest model with the other two classifiers using the testing data:\n"]}, {"cell_type": "code", "execution_count": 14, "id": "b94497d7", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Train</th>\n", "      <th>Test</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>KNN</th>\n", "      <td>81.500647</td>\n", "      <td>67.829457</td>\n", "    </tr>\n", "    <tr>\n", "      <th>DecisionTree</th>\n", "      <td>89.133247</td>\n", "      <td>83.333333</td>\n", "    </tr>\n", "    <tr>\n", "      <th>RandomForest</th>\n", "      <td>94.954722</td>\n", "      <td>86.046512</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                  Train       Test\n", "KNN           81.500647  67.829457\n", "DecisionTree  89.133247  83.333333\n", "RandomForest  94.954722  86.046512"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["# Retrain the KNN model with k=7\n", "from sklearn.?? import ??Classifier\n", "knn = KNeighborsClassifier(n_neighbors=??)\n", "knn.fit(X_train, y_train) \n", "\n", "# Retrain the decision treen model with max_depth=6\n", "from sklearn.?? import ??Classifier\n", "class_tree = DecisionTreeClassifier(max_depth=??,\n", "                                   min_samples_split=??) \n", "class_tree.fit(X_train, y_train)\n", "\n", "# Retrain the random forest model with max_depth=6\n", "from sklearn.?? import ??Classifier\n", "rf = RandomForestClassifier(n_estimators=??,\n", "                            max_depth=??,\n", "                            random_state=??)\n", "rf.fit(X_train, y_train)\n", "\n", "# Prepare the data frame for evaluation metrics\n", "accuracies = pd.DataFrame(columns=['Train', 'Test'], index=[\"KNN\", 'DecisionTree', 'RandomForest'])\n", "model_dict = {'KNN': knn, 'DecisionTree': class_tree, 'RandomForest': rf}\n", "\n", "# Evaluate the accuraccies of the 3 predictive models\n", "from sklearn.metrics import accuracy_score \n", "for name, model in model_dict.items():\n", "    accuracies.loc[name, 'Train'] = accuracy_score(y_true=y_train, y_pred=model.predict(X_train))                                                                                                                  \n", "    accuracies.loc[name, 'Test'] = accuracy_score(y_true=y_test, y_pred=model.predict(X_test))   \n", "\n", "# Show results in percentage\n", "100*accuracies  \n", "  \n"]}, {"cell_type": "markdown", "id": "b8a80c95", "metadata": {}, "source": ["Let use the bar graph to virtally compare the 3 predictive models to evaluate the accuracy of both training and testing sets."]}, {"cell_type": "code", "execution_count": 15, "id": "4a8916c4", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAasAAAD4CAYAAABSfMmAAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjQuMywgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/MnkTPAAAACXBIWXMAAAsTAAALEwEAmpwYAAAT/0lEQVR4nO3dfbRddX3n8feHPFyUMImN4HJBJfgAih2IQ+pDWmoCWijogB0YmUEFcRaDHUGlLGFhZTK62iJt0QlOy6KWIl2rU6viIMbRisMlKCgSDQ8Rg1YZTQcRUC4QBUn4zh9nQ66Xm9yTcO85P5P3a62sc85++O3v+a4kn/vbe99zUlVIktSy3YZdgCRJUzGsJEnNM6wkSc0zrCRJzTOsJEnNmz3sAnZWCxYsqBe+8IXDLqMJGzduZI899hh2GU2wF1vYiy3sxRZr1qy5r6r2mrjcsJohz3nOc7j55puHXUYTRkdHWbZs2bDLaIK92MJebGEvtkjyfydb7mlASVLzDCtJUvMMK0lS87xmJUmNeOyxx9iwYQOPPPLIsEuZcbvvvjv77rsvc+bM6Wt7w0qSGrFhwwb23HNPFi1aRJJhlzNjqor777+fDRs2sP/++/e1j6cBJakRjzzyCAsXLtypgwogCQsXLtyuGaRhJUkN2dmD6gnb+z4NK0lS87xmJUmNWnTuqmkd764Ljtnm+vvvv58jjjgCgB/96EfMmjWLvfbqfZjETTfdxNy5c7e6780338wVV1zBypUrp6/gcQwrSRIACxcuZO3atQCsWLGCefPmcfbZZz+5ftOmTcyePXlsLFmyhCVLlsxYbZ4GlCRt1SmnnMJZZ53F8uXLOeecc7jppptYunQpL3vZy1i6dCnr168Heh8Z9brXvQ7oBd2pp57KsmXLeP7znz8tsy1nVpKkbbrzzju55pprmDVrFg8++CCrV69m9uzZXHPNNZx33nl86lOfeso+3/72t7n22mt56KGHOPDAA3n729/e9+9UTcawkiRt0wknnMCsWbMAGBsb4+STT+Y73/kOSXjssccm3eeYY45hZGSEkZER9t57b+655x723XffHa7B04CSpG0a//Ul73vf+1i+fDm33347V1999VZ/V2pkZOTJ57NmzWLTpk1PqwbDSpLUt7GxMfbZZx8ALr/88oEd19OAktSoqW41H4b3vOc9nHzyyVx00UUcfvjhAztuqmpgB9ulrJhvY6VWrBgbdgXb9MSXL95xxx285CUvGXY5AzPZ+02ypqqecg+8pwElSc0zrCRJzTOsJEnNM6wkSc0zrCRJzTOsJEnN8/esJKlVK+ZP83jbvoX/6XxFCPRuwZ87dy5Lly6dnnrHMawkScDUXxEyldHRUebNmzcjYeVpQEnSVq1Zs4ZXv/rVHHrooRx55JHcfffdAKxcuZKDDjqIgw8+mBNPPJG77rqLSy65hA996EMsXryY66+/flrrcGYlSZpUVXHGGWdw1VVXsddee/Hxj3+c9773vVx22WVccMEFfP/732dkZIQHHniABQsWcPrpp2/3bKxfhpUkaVKPPvoot99+O6997WsB2Lx5M8997nMBOPjggznppJM47rjjOO6442a8FsNKkjSpquKlL30pN95441PWrVq1itWrV/OZz3yGD3zgA6xbt25Ga/GalSRpUiMjI9x7771PhtVjjz3GunXrePzxx/nhD3/I8uXLufDCC3nggQd4+OGH2XPPPXnooYdmpBZnVpLUqiF/Wvxuu+3GJz/5Sc4880zGxsbYtGkT73rXuzjggAN405vexNjYGFXFu9/9bhYsWMDrX/96jj/+eK666iouvvhiDjvssGmrZZcJqyQPV9W87vnRwH8HjgBOBd4DLKqqH0+ybQEXVdUfdq/PBuZV1YrBvwtJGowVK1Y8+Xz16tVPWf/lL3/5KcsOOOAAbr311hmpZ5c7DZjkCOBi4Kiq+kG3+D7gD7eyy6PA7yd59iDqkyQ91S4VVkkOA/4aOKaq/nncqsuANyb5tUl22wRcCrx7ACVKkiaxK4XVCHAVcFxVfXvCuofpBdY7t7Lv/wBOSjLNn30iSb9sV/n29u19n7vMNSvgMeAG4G1MHkorgbVJ/mLiiqp6MMkVwJnAz7d2gCSnAacB7HfOZ6ejZknT4dxVw65gap9fxXt/ZyE/2+0HzH7mvyIJ+8+fNeyqZkRVMTY2xsaNGxkdHe1rn10prB4H/j1wTZLzqupPxq+sqgeS/D3wB1vZ/8PAN4C/3doBqupSeqcMWXTuql3jxyNJ0+bir/2UM4D9FtxHCHOe9YxhlzRjdt99dw455BDmzJnT1/a7UlhRVT9L8jrg+iT3VNXfTNjkIuDrTNKXqvpJkn+kNzO7bOarlbSrefDRx/nj1fc/+fquC44ZYjVt2ZWuWQG90AGOAv4oybET1t0HfJre9a3J/AXgXYGSNGC7zMzqid+b6p7/ENi/e3nVhO3OAs7ayn73AM+c2UolSRPtcjMrSdKvHsNKktQ8w0qS1DzDSpLUPMNKktQ8w0qS1DzDSpLUPMNKktQ8w0qS1DzDSpLUPMNKktQ8w0qS1DzDSpLUvF3mU9cHbeTTZ7F+/fphl9GE0dFRli1bNuwymmAvtrAXW9iLqTmzkiQ1z7CSJDXPsJIkNc+wkiQ1z7CSJDXPsJIkNc+wkiQ1z7CSJDXPsJIkNc+wkiQ1z7CSJDXPsJIkNc+wkiQ1z7CSJDXPsJIkNc+wkiQ1z7CSJDXPsJIkNc+wkiQ1z7CSJDXPsJIkNc+wkiQ1z7CSJDXPsJIkNc+wkiQ1z7CSJDXPsJIkNc+wkiQ1z7CSJDXPsJIkNc+wkiQ1z7CSJDXPsJIkNc+wkiQ1z7CSJDUvVTXsGnZOK+bbWEkzY8XYsCuYMUnWVNWSicudWUmSmmdYSZKaZ1hJkppnWEmSmmdYSZKaZ1hJkppnWEmSmmdYSZKaZ1hJkppnWEmSmmdYSZKaZ1hJkppnWEmSmmdYSZKa97TDKsnmJGuTrEtyS5KzkuzQuEnen+Q121h/epK37MC4R3Y1rk3ycJL13fMrdqROSdJgzZ6GMX5eVYsBkuwN/D0wH/iv2ztQVZ0/xfpLdqTAqvoC8IWuxlHg7Kq6efw2SWZV1eYdGV+SNLOm9TRgVf0YOA14R3pmJfmzJF9PcmuS//zEtknek+S2bjZ2Qbfs8iTHd88vSPKtbr8/75atSHJ293xxkq926z+d5Fnd8tEkH0xyU5I7kxy2tXqT3JXk/CRfBk5I8rtJbkzyjSSfSDKv2+7QJNclWZPkC0meO519kyRt23TMrH5JVX2vOw24N3AsMFZVv5lkBPhKkn8CXgwcB7yiqn6W5NfGj9G9fgPw4qqqJAsmOdQVwBlVdV2S99Obyb3rifdVVS9PcnS3fKunFoFHquq3kzwbuBJ4TVVtTHIOcFaSPwUuBo6tqnuTvBH4Y+DUiQMlOY1eWLPfOZ+dqlWStGPOXTVtQ11+1B7TNtZMmvaw6qR7/F3g4CdmS/ROD76IXnj8bVX9DKCqfjJh/weBR4CPJlkF/NL//EnmAwuq6rpu0ceAT4zb5MrucQ2waIpaP949vhI4iF6gAswFbgQOBH4D+GK3fBZw92QDVdWlwKUAi85d5dfaS2resmXLhl1CX6Y9rJI8H9gM/JheaJ3RXTMav81RwFb/M6+qTUleDhwBnAi8Azh8O8p4tHvczNTvceMTZQFfrKr/MKHWfw2sq6pXbcfxJUnTaFqvWSXZC7gE+EhVFb2bGt6eZE63/oAkewD/BJya5Jnd8omnAecB86vqc/RO7S0ev76qxoCfjrse9WbgOp6erwK/leSFXQ3PTHIAsB7YK8mruuVzkrz0aR5LkrQdpmNm9Ywka4E5wCbg74CLunUfpXca7hvpnUO7Fziuqj6fZDFwc5JfAJ8Dzhs35p7AVUl2pzfjefckxz0ZuKQLvO8Bb306b6K7HnUK8D+762sAf1RVd3anMVd2px9nAx8G1j2d40mS+pfeBEjTzWtWkn4V3HXBMcMu4ZckWVNVSyYu9xMsJEnNM6wkSc0zrCRJzTOsJEnNM6wkSc0zrCRJzTOsJEnNM6wkSc0zrCRJzTOsJEnNM6wkSc0zrCRJzZupL1/c5Y18+izWr18/7DKaMDo6+ivzBW8zzV5sYS+2sBdTc2YlSWqeYSVJap5hJUlqnmElSWqeYSVJap5hJUlqnmElSWqeYSVJap5hJUlqnmElSWqeYSVJap5hJUlqnmElSWqeYSVJap5hJUlqnmElSWqeYSVJap5hJUlqnmElSWqeYSVJap5hJUlqnmElSWqeYSVJap5hJUlqnmElSWqeYSVJap5hJUlqnmElSWqeYSVJap5hJUlqnmElSWqeYSVJap5hJUlqnmElSWqeYSVJal6qatg17JxWzLexkoZnxdiwK9ghSdZU1ZKJy51ZSZKaZ1hJkppnWEmSmmdYSZKaZ1hJkppnWEmSmmdYSZKaZ1hJkppnWEmSmmdYSZKaZ1hJkppnWEmSmmdYSZKaZ1hJkpo3ZVgl2ZxkbZLbk1ydZMF0HDjJKUk+Mk1j3ZXktq7OtUmWTse4kxxncZKjZ2JsSdLW9TOz+nlVLa6q3wB+AvyXGa5pRy3v6lxcVTf0s0OS2dt5jMWAYSVJA7a9pwFvBPYBSPLyJDck+Wb3eGC3/JQkVyb5fJLvJLnwiZ2TvDXJnUmuA35r3PL9knwpya3d4/O65Zcn+ask1yb5XpJXJ7ksyR1JLt9WoVOMeVGSa4EPJnlBV+uaJNcneXG33QndbPKWJKuTzAXeD7yxm729cTt7J0naQX3PLJLMAo4A/qZb9G3gd6pqU5LXAH8C/Ltu3WLgZcCjwPokFwObgP8GHAqMAdcC3+y2/whwRVV9LMmpwErguG7ds4DDgX8LXE0v5P4T8PUki6tqbbfdtUk2A49W1SumGPMA4DVVtTnJl4DTq+o7SV4B/GV3vPOBI6vqX5IsqKpfJDkfWFJV7+i3b5Kkp6+fsHpGkrXAImAN8MVu+XzgY0leBBQwZ9w+X6qqMYAk3wL2A54NjFbVvd3yj9MLDYBXAb/fPf874MJxY11dVZXkNuCeqrqt239dV9PabrvlVXXfuP22NeYnuqCaBywFPpHkiXUj3eNXgMuT/CNw5Tb686QkpwGnAex3zmf72UWSZsa5q4Zy2MuP2mNGxu0nrH5eVYuTzAc+S++a1UrgA8C1VfWGJIuA0XH7PDru+eZxx6k+6xq/3RNjPT5h3Mf7rH+yMTd2j7sBD1TV4qdsXHV6N9M6Blib5CnbTLLPpcClAIvOXdXve5WkncayZctmZNy+r1l1M6UzgbOTzKE3s/qXbvUpfQzxNWBZkoXd/ieMW3cDcGL3/CTgy/3WtQ1TjllVDwLfT3ICQHoO6Z6/oKq+VlXnA/cBvw48BOw5DbVJkrbDdt1gUVXfBG6hFwIXAn+a5CvArD72vRtYQe8mjWuAb4xbfSbw1iS3Am8G3rk9dW1Fv2OeBLwtyS3AOuDYbvmfdbfD3w6spve+rwUO8gYLSRqsVHm2aiZ4GlDSruiuC455WvsnWVNVSyYu9xMsJEnNM6wkSc0zrCRJzTOsJEnNM6wkSc0zrCRJzTOsJEnNM6wkSc0zrCRJzTOsJEnNM6wkSc0zrCRJzTOsJEnN254vL9R2GPn0Waxfv37YZTRhdHR0xr6Q7VeNvdjCXmxhL6bmzEqS1DzDSpLUPMNKktQ8w0qS1DzDSpLUPMNKktQ8w0qS1DzDSpLUPMNKktQ8w0qS1DzDSpLUPMNKktQ8w0qS1DzDSpLUPMNKktQ8w0qS1DzDSpLUvFTVsGvYKSV5CPCrgnueDdw37CIaYS+2sBdb2Ist9quqvSYu9GvtZ876qloy7CJakORme9FjL7awF1vYi6l5GlCS1DzDSpLUPMNq5lw67AIaYi+2sBdb2Ist7MUUvMFCktQ8Z1aSpOYZVpKk5hlWT0OSo5KsT/LdJOdOsj5JVnbrb03yb4ZR56D00Y+Tuj7cmuSGJIcMo85BmKoX47b7zSSbkxw/yPoGqZ9eJFmWZG2SdUmuG3SNg9LHv5H5Sa5OckvXi7cOo84mVZV/duAPMAv4Z+D5wFzgFuCgCdscDfxvIMArga8Nu+4h92Mp8Kzu+e/trP3opxfjtvs/wOeA44dd9xD/XiwAvgU8r3u997DrHmIvzgM+2D3fC/gJMHfYtbfwx5nVjns58N2q+l5V/QL4B+DYCdscC1xRPV8FFiR57qALHZAp+1FVN1TVT7uXXwX2HXCNg9LP3w2AM4BPAT8eZHED1k8v/iNwZVX9AKCqdtZ+9NOLAvZMEmAevbDaNNgy22RY7bh9gB+Oe72hW7a92+wstve9vo3erHNnNGUvkuwDvAG4ZIB1DUM/fy8OAJ6VZDTJmiRvGVh1g9VPLz4CvAT4f8BtwDur6vHBlNc2P25px2WSZRN/D6CfbXYWfb/XJMvphdVvz2hFw9NPLz4MnFNVm3s/RO+0+unFbOBQ4AjgGcCNSb5aVXfOdHED1k8vjgTWAocDLwC+mOT6qnpwhmtrnmG14zYAvz7u9b70fhra3m12Fn291yQHAx8Ffq+q7h9QbYPWTy+WAP/QBdWzgaOTbKqq/zWQCgen338n91XVRmBjktXAIcDOFlb99OKtwAXVu2j13STfB14M3DSYEtvlacAd93XgRUn2TzIXOBH4zIRtPgO8pbsr8JXAWFXdPehCB2TKfiR5HnAl8Oad8Kfm8absRVXtX1WLqmoR8EngD3bCoIL+/p1cBRyWZHaSZwKvAO4YcJ2D0E8vfkBvhkmS5wAHAt8baJWNcma1g6pqU5J3AF+gd5fPZVW1Lsnp3fpL6N3ldTTwXeBn9H5q2in12Y/zgYXAX3Yzik21E37SdJ+92CX004uquiPJ54FbgceBj1bV7cOremb0+ffiA8DlSW6jd9rwnKryq0Pw45YkSb8CPA0oSWqeYSVJap5hJUlqnmElSWqeYSVJap5hJUlqnmElSWre/wfbvB98No9uYAAAAABJRU5ErkJggg==\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots()\n", "accuracies.sort_values(by='Test', ascending=False).plot(kind='barh', ax=ax, zorder=3)\n", "ax.grid(zorder=0); \n"]}, {"cell_type": "markdown", "id": "261213a1", "metadata": {}, "source": ["From the accuracy results, discuss with your team on the following questions:\n", "\n", "1. Which predictive model would your team choose to adopt? And why? \n", "2. How will you improve the results of the accuracy further? \n", "3. Is Accuracy result alone is sufficient for you to justify that it is indeed a good predictive model? Why or why not? "]}, {"cell_type": "markdown", "id": "4e397e2f", "metadata": {}, "source": ["Your response here ..."]}, {"cell_type": "markdown", "id": "83049004", "metadata": {}, "source": ["***\n", "You may save your 3 models for future use. "]}, {"cell_type": "code", "execution_count": null, "id": "ab4bcf81", "metadata": {}, "outputs": [], "source": ["import pickle as pk\n", "\n", "...\n", "...\n", "...\n", "\n", "print(\"Models Saved\")"]}, {"cell_type": "markdown", "id": "f5b13454", "metadata": {}, "source": ["***"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}