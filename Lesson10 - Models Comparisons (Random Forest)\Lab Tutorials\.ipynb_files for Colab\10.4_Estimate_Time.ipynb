{"cells": [{"cell_type": "markdown", "id": "fa9fe4b6", "metadata": {"id": "fa9fe4b6"}, "source": ["## Estimate the Completion Time of the Washing Cycle\n", "\n", "\n", "Given that a new data with the respective parameters shown below were captured in the washing machine (see 'input1.csv' file):  \n", "\n", "![new_data1.png](attachment:new_data1.png)\n", "\n", "We shall use the predictive model trained earlier to predict the wash cycle, and estimate the time needed for the washing machine to complete the entire washing. The tasks to be executed are as follows:\n", "\n", "1. Load this new data from the 'new_data1.csv' file\n", "2. <PERSON>ad the predictive model that produced the best evaluation result earlier\n", "3. Predict the wash cycle from the 'best' model\n", "4. Calculate the estimated time needed to wait before the sequence of washing is completed\n", "\n", "***"]}, {"cell_type": "code", "execution_count": 1, "id": "c3187a38", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "c3187a38", "executionInfo": {"status": "ok", "timestamp": 1749717237771, "user_tz": -480, "elapsed": 20630, "user": {"displayName": "<PERSON>", "userId": "08280565725279349310"}}, "outputId": "91277655-0700-48d0-f301-d2f820513b69"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Mounted at /content/drive\n"]}], "source": ["from google.colab import drive\n", "\n", "drive.mount('/content/drive', force_remount=True)"]}, {"cell_type": "code", "execution_count": 2, "id": "81f8cd73", "metadata": {"id": "81f8cd73", "executionInfo": {"status": "ok", "timestamp": 1749717240324, "user_tz": -480, "elapsed": 4, "user": {"displayName": "<PERSON>", "userId": "08280565725279349310"}}}, "outputs": [], "source": ["ROOT_DIR = '/content/drive/MyDrive/C3790C'"]}, {"cell_type": "code", "execution_count": null, "id": "e3650204", "metadata": {"id": "e3650204", "outputId": "ec9bb951-f58b-4409-8331-b46a09d7023c"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   index_col       avC      avP       sdC         sdP         avR  maxC  maxP  \\\n", "0          4  3.666701  658.582  3.982487  769.852323  468.296401  0.64    72   \n", "\n", "      stdCR      stdCP       AvRR  \n", "0  0.807714  18.473964  592.60328  \n"]}], "source": ["# Step 1: Input necessary Python libraries\n", "import pandas as pd\n", "\n", "# Loading the data from new input\n", "data_path  = ?? + '/data/input1.csv'\n", "currentdf  = pd.read_csv(data_path)\n", "\n", "input1 = currentdf.iloc[:, 1:]\n", "print (input1)"]}, {"cell_type": "code", "execution_count": null, "id": "98303660", "metadata": {"id": "98303660", "outputId": "0a4d3a5c-8080-4a4b-a8ce-8caaf7589985"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model loaded\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\ProgramData\\Anaconda3\\lib\\site-packages\\sklearn\\base.py:310: UserWarning: Trying to unpickle estimator DecisionTreeClassifier from version 0.24.1 when using version 0.24.2. This might lead to breaking code or invalid results. Use at your own risk.\n", "  warnings.warn(\n", "C:\\ProgramData\\Anaconda3\\lib\\site-packages\\sklearn\\base.py:310: UserWarning: Trying to unpickle estimator RandomForestClassifier from version 0.24.1 when using version 0.24.2. This might lead to breaking code or invalid results. Use at your own risk.\n", "  warnings.warn(\n"]}], "source": ["# Step 2: Load the predictive model that has produced the best result earlier\n", "import pickle as pk\n", "\n", "model_filename = ?? + \"./model/??.mdl\" # Replace ?? with your best model saved in the 'model' folder earlier\n", "model = pk.load(open(model_filename, 'rb'))\n", "print(\"Model loaded\")"]}, {"cell_type": "code", "execution_count": null, "id": "924c6a24", "metadata": {"id": "924c6a24", "outputId": "4ff0c687-78c2-45ed-b8f5-4ca09bc82d28"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CottonWash\n"]}], "source": ["# Step 3: Predicting the wash cycle of the given new data\n", "mode = model.predict(input1)\n", "print (mode[0])"]}, {"cell_type": "markdown", "id": "eada0bdf", "metadata": {"id": "eada0bdf"}, "source": ["Before estimating the completion time, let's understand the washing mode and it's sequence. As shown in the table below, there are 3 types of washing mode (Daily, Cotton & Bedding), 3 sequences of the washing cycle (Wash, Rinse & Spin) in each mode, and the total time steps needed to complete each washing sequence.\n", "\n", "![type.png](attachment:type.png)\n", "\n", "In the table above, assuming each time step required 6 minutes to complete. Hence, the total amount of time (in minutes) required to complete each wash sequence is calculated in the last column of the table.\n"]}, {"cell_type": "markdown", "id": "a7ef0162", "metadata": {"id": "a7ef0162"}, "source": ["For the dataset shown below, the 'index_col' column in the CSV file indicates the time step in each wash cycle (see Column C).\n", "\n", "![dataset.png](attachment:dataset.png)\n", "\n", "Given that each time step takes 6 minutes to complete, index_col = 3 implies that this specific wash cycle called 'CottonWash' has completed 3 * 6 =  18 minutes of washing. Hence, the estimate time to completion for this wash sequence is 84 - 18 = 66 minutes. And the estimated time to complete the entire cotton sequence is 66 + 24 + 24 = 114 minutes. That is how long the customer will need to wait before the washing is complete.  \n", "\n", "Using the data from 'input1.csv' file, let's estimate the total time needed for the customer to wait before she can collect her laundry.\n", "***"]}, {"cell_type": "code", "execution_count": null, "id": "868f0b42", "metadata": {"id": "868f0b42", "outputId": "c494bc17-8b14-4923-f466-2b5bbe2d2b23"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Current wash cycle: CottonWash\n", "Estimate time to completion: 108.0 mins.\n"]}], "source": ["# Step 4: Calculate the estimated time needed to wait before the sequence of washing is completed\n", "\n", "# From the 'new_input1.csv' file, extract the time step from dataframe\n", "t_step = input1.iloc[0][0]\n", "\n", "# <PERSON><PERSON><PERSON>ine the total time steps to complete the entire wash sequence\n", "if mode == ('DailyWash' or 'DailyRaise' or 'DailySpin'):\n", "    total_steps = 13\n", "elif mode == ('CottonWash' or 'CottonRaise' or 'CottonSpin'):\n", "    total_steps = 22\n", "else:\n", "    total_steps = 17\n", "\n", "toc = (total_steps - t_step) * 6\n", "\n", "print (\"Current wash cycle: \" + mode[0])\n", "print (\"Estimate time to completion: \" + str(toc) + \" mins.\")"]}, {"cell_type": "markdown", "id": "45792982", "metadata": {"id": "45792982"}, "source": ["From the data collected in 'input2.csv' file, repeat $Step 4$ again to predict the wash cycle and the estimated time needed to complete the entire wash sequence.  \n"]}, {"cell_type": "code", "execution_count": null, "id": "d713e97b", "metadata": {"id": "d713e97b"}, "outputs": [], "source": ["# Insert your code here ....\n"]}, {"cell_type": "markdown", "id": "b0b92af5", "metadata": {"id": "b0b92af5"}, "source": ["Explore using different values in 'input2.csv' to predict the new wash cycle and the new estimated time to completion.\n"]}, {"cell_type": "code", "execution_count": null, "id": "5b44271a", "metadata": {"id": "5b44271a"}, "outputs": [], "source": ["# Insert your code here ....\n"]}, {"cell_type": "markdown", "id": "5f1cd282", "metadata": {"id": "5f1cd282"}, "source": ["***"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}, "colab": {"provenance": []}}, "nbformat": 4, "nbformat_minor": 5}