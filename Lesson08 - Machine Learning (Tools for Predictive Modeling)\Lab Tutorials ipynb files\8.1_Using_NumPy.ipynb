{"cells": [{"cell_type": "markdown", "metadata": {"id": "ExK7xYMQMnmD"}, "source": ["# C379 Emerging Technologies"]}, {"cell_type": "markdown", "metadata": {"id": "aNZNuiNQMnmL"}, "source": ["# Lesson 8 - Scientific Computing Library for Python"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "bCl5IIo3MnmP", "executionInfo": {"status": "ok", "timestamp": 1749715984630, "user_tz": -480, "elapsed": 27902, "user": {"displayName": "<PERSON>", "userId": "08280565725279349310"}}, "outputId": "3f620b1a-f7f6-400d-e1fa-1fb263a7d8d0"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Mounted at /content/drive\n"]}], "source": ["from google.colab import drive\n", "\n", "drive.mount('/content/drive', force_remount=True)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"id": "RLShbvbQMnmS", "executionInfo": {"status": "ok", "timestamp": 1749715986090, "user_tz": -480, "elapsed": 13, "user": {"displayName": "<PERSON>", "userId": "08280565725279349310"}}}, "outputs": [], "source": ["ROOT_DIR = '/content/drive/MyDrive/C3790C'"]}, {"cell_type": "markdown", "metadata": {"id": "6t1C28jjMnmU"}, "source": ["### Using NumPy\n", "NumPy is the foundational library for the scientific computing library for the Python ecosystem. The main libraries in the ecosystem - pandas, matplotlib, SciPy and scikit-learn are based on NumPy. Please refer to http://numpy.org for more information.\n", "\n", "As it is a foundational library, it is important to know at least the basics of NumPy."]}, {"cell_type": "markdown", "metadata": {"id": "xAxTt-zOMnmV"}, "source": ["### NumPy Tutorial\n", "Here are a couple of motivating examples about why vectorization is needed when doing any kind of scientific computing. Let's perform a couple of simple calculations with Python.\n", "\n", "First, let's say you have some distances and times and you would like to calculate the speeds:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "juxCNEDBMnmV", "executionInfo": {"status": "ok", "timestamp": 1749715990174, "user_tz": -480, "elapsed": 12, "user": {"displayName": "<PERSON>", "userId": "08280565725279349310"}}, "outputId": "6aab3131-f084-4d58-c09f-033f2a069284"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[33.333333333333336,\n", " 31.914893617021278,\n", " 30.909090909090907,\n", " 21.666666666666668]"]}, "metadata": {}, "execution_count": 3}], "source": ["distances = [10, 15, 17, 26]\n", "times = [0.3, 0.47, 0.55, 1.20]\n", "\n", "# Calculate speeds with Python\n", "speeds = []\n", "for i in range (len(distances)):\n", "    speeds.append(distances[i]/times[i])\n", "\n", "speeds"]}, {"cell_type": "markdown", "metadata": {"id": "n6BrjVP8MnmY"}, "source": ["An alternative to accomplish the same in Python methodlogy would be the following:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "oz9cpIadMnmZ", "outputId": "a04a40cb-7862-4d38-9913-08ed48e57d00"}, "outputs": [{"data": {"text/plain": ["[33.333333333333336,\n", " 31.914893617021278,\n", " 30.909090909090907,\n", " 21.666666666666668]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["# An alternative\n", "speeds = [d/t for d,t in zip(distances, times)]\n", "speeds"]}, {"cell_type": "markdown", "metadata": {"id": "lRx8I9JKMnma"}, "source": ["For the second motivating example, let's say you have a list of product qualities and their respective prices, and you would like to calculate the total of the purchase. The code in Python would look something like this:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "FyvgwL4GMnmb", "outputId": "0a3b990a-2af8-4eee-ec00-de00170ee98c"}, "outputs": [{"data": {"text/plain": ["157.1"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["product_quantities = [13, 5, 6, 10, 11]\n", "prices = [1.2, 6.5, 1.0, 4.8, 5.0]\n", "total = sum([q*p for q,p in zip(product_quantities, prices)])\n", "total"]}, {"cell_type": "markdown", "metadata": {"id": "TWbtpkd8Mnmb"}, "source": ["The point of these two examples is that, for this type of calculation, you need to perform operations element by element and in Python (as well as most programming languages) you do it by using for loops or list comprehensions (which are just convenient ways of writing for loops).\n", "\n", "Vectorization is a style of computer programming where operations are applied to arrays of individual elements. In other words, a vectorized operation is the application of the operation, element by element, without explicitly doing it with for loops.\n", "\n", "Now, let's take a look at the NumPy approach to doing the former operations:\n", "\n", "First, let's import the NumPy library and do the speeds calculation. As you can see, by adding the mathematical definition of speed, this is very easy and natural."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "5f1lmJepMnmc", "outputId": "11e6ba59-bc63-4704-8641-de41f3302226"}, "outputs": [{"data": {"text/plain": ["array([33.33333333, 31.91489362, 30.90909091, 21.66666667])"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["import numpy as np\n", "\n", "# Calculate the speeds\n", "distances = np.array([10, 15, 17, 26])\n", "times = np.array([0.3, 0.47, 0.55, 1.20])\n", "speeds = distances/times\n", "speeds"]}, {"cell_type": "markdown", "metadata": {"id": "tU973P3HMnmd"}, "source": ["Again for the purchase calculation, the code for running this calculation is much easier and more natural:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "myL8FliiMnmd", "outputId": "896b40a2-47a0-4f75-f47d-620950db2119"}, "outputs": [{"data": {"text/plain": ["157.1"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# Calaculate the total of a purchase\n", "product_quantities = np.array([13, 5, 6, 10, 11])\n", "prices = np.array([1.2, 6.5, 1.0, 4.8, 5.0])\n", "total = (product_quantities*prices).sum()\n", "total"]}, {"cell_type": "markdown", "metadata": {"id": "c24FL0eFMnme"}, "source": ["After running this calculation, you will get the same total: 157.1\n", "\n", "Now, let learn some of the basics of array creation, main attributes, and operations. This is of course by no means a complete introduction, but it will be enough for you to have a basic understanding of how NumPy arrays work.\n", "\n", "As you saw earlier, we can create arrays from lists like:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "Vjys1w0PMnme"}, "outputs": [], "source": ["# Array from lists\n", "product_quantities = [13, 5, 6, 10, 11]\n", "prices = [1.2, 6.5, 1.0, 4.8, 5.0]\n", "product_quantities = np.array(product_quantities)\n", "prices = np.array(prices)"]}, {"cell_type": "markdown", "metadata": {"id": "PJRxQplgMnme"}, "source": ["If you pass a list of lists to np.array(), it will create a two-dimensional array. If passed a list of lists (three nested lists), it will create a three-dimentional array, and so on and so forth:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "0qvO2M-cMnmf", "outputId": "f65c7c92-bfad-46f4-f074-ba319cdff03e"}, "outputs": [{"data": {"text/plain": ["array([[1, 2],\n", "       [3, 4]])"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["A = np.array([[1, 2], [3, 4]])\n", "A"]}, {"cell_type": "markdown", "metadata": {"id": "SOUqVHUTMnmf"}, "source": ["Take a look at some of the array's main attributes. Let's create some arrays containing randomly generated numbers:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "OlWPgT3tMnmp", "outputId": "ecca453d-b049-4f78-ea36-44b9f24ebf3c"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[5 0 3 3 7 3 5 2 4 7 6 8] \n", "\n", "[[8 1 6 7]\n", " [7 8 1 5]\n", " [8 4 3 0]] \n", "\n", "[[[3 5 0 2 3]\n", "  [8 1 3 3 3]\n", "  [7 0 1 0 4]\n", "  [7 3 2 7 2]]\n", "\n", " [[0 0 4 5 5]\n", "  [6 8 4 1 4]\n", "  [8 1 1 7 3]\n", "  [6 7 2 0 3]]\n", "\n", " [[5 4 4 6 4]\n", "  [4 3 4 4 8]\n", "  [4 3 7 5 5]\n", "  [0 1 5 3 0]]] \n", "\n"]}], "source": ["# Set a seed for reproducibility\n", "np.random.seed(0)\n", "\n", "# 1D array\n", "x1 = np.random.randint(low=0, high=9, size=12)\n", "\n", "# 2D array\n", "x2 = np.random.randint(low=0, high=9, size=(3, 4))\n", "\n", "# 3D array\n", "x3 = np.random.randint(low=0, high=9, size=(3, 4, 5))\n", "\n", "print (x1, '\\n')\n", "print (x2, '\\n')\n", "print (x3, '\\n')"]}, {"cell_type": "markdown", "metadata": {"id": "L_Z393gbMnmr"}, "source": ["Some important array attributes are as follows:\n", "\n", "- ndarray.ndim: The number of dimensions (axes) of the array.\n", "- ndarray.shape: The dimensions of the array. This tuple of integers indicates the size of the array in each dimension.\n", "- ndarray.size: The total number of elements of the array. This is equal to the product of the elements of shape.\n", "- ndarray.dtype: An object describing the type of the elements in the array. One can create or specify dtype's using standard Python types. Moreover, NumPy provides types of its own. numpy.int32, numpy.int16 and numpy.float64 are some examples:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "LY4sja6ZMnms", "outputId": "699a5873-3b5a-473e-85c9-c4fcbc75a2c1"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["x3 ndim:  3\n", "x3 shape:  (3, 4, 5)\n", "x3 size:  60\n", "x3 dtype:  int32\n"]}], "source": ["print (\"x3 ndim: \", x3.ndim)\n", "print (\"x3 shape: \", x3.shape)\n", "print (\"x3 size: \", x3.size)\n", "print (\"x3 dtype: \", x3.dtype)"]}, {"cell_type": "markdown", "metadata": {"id": "c5eNizFTMnmy"}, "source": ["One-dimensional arrays can be indexed, sliced and iterated over, just like lists or other Python sequences:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": true, "id": "2I0RWFY3Mnmy", "outputId": "125b57eb-3554-4af5-8d71-32c11f724ed2"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[5 0 3 3 7 3 5 2 4 7 6 8]\n", "3\n", "[3 3 7]\n", "8\n"]}], "source": ["print (x1)\n", "print (x1[5])   # Element at index 5\n", "print (x1[2:5]) # Slice from elements in indexes 2, 3 & 4\n", "print (x1[-1])  # Last element of the array"]}, {"cell_type": "markdown", "metadata": {"id": "kgCBCpGFMnmy"}, "source": ["Multi-dimensional arrays have one index per axis. These indices are given in a tuple separated by commas:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "hcE0VsiOMnmz", "outputId": "1cba7ce6-c28d-4088-8e95-ea51687dbe6f"}, "outputs": [{"data": {"text/plain": ["array([[ 1,  2,  3,  4],\n", "       [ 5,  6,  7,  8],\n", "       [ 9, 10, 11, 12],\n", "       [13, 14, 15, 16],\n", "       [17, 18, 19, 20]])"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["# Integers from 1 to 20\n", "one_to_twenty = np.arange(1, 21)\n", "\n", "# Tranform to 5-row by 4-column matrix\n", "my_matrix = one_to_twenty.reshape(5, 4)\n", "\n", "my_matrix"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "gYLm4UJ8Mnm1", "outputId": "ac084124-0d3e-4fbc-de4d-26af189aca31"}, "outputs": [{"data": {"text/plain": ["12"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["# Display element in row 3, column 4 (remember Python is zero indexed)\n", "my_matrix[2, 3]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "Y_TQbQnfMnm2", "outputId": "9c62cb18-1685-4377-8987-abe9e6058af5"}, "outputs": [{"data": {"text/plain": ["array([ 2,  6, 10, 14, 18])"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["# Display row in the 2nd column of my_matrix\n", "my_matrix[:, 1]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "kI6Fde5PMnm2", "outputId": "6262c845-0909-40ff-b4b4-d8c74685e069"}, "outputs": [{"data": {"text/plain": ["array([4, 8])"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["# Display 1st & 2nd row of the last column\n", "my_matrix[0:2, -1]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "0hZfmUG3Mnm3", "outputId": "0796ec28-cd66-4e6f-9ee6-fab4705e5d62"}, "outputs": [{"data": {"text/plain": ["array([[-1,  2,  3,  4],\n", "       [ 5,  6,  7,  8],\n", "       [ 9, 10, 11, 12],\n", "       [13, 14, 15, 16],\n", "       [17, 18, 19, 20]])"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["# Set the 1st element to -1\n", "my_matrix[0, 0] = -1\n", "my_matrix"]}, {"cell_type": "markdown", "metadata": {"id": "IKD9DrRpMnm3"}, "source": ["Finally, let's perform some mathematical operations on the former matrix, just to have some examples of how vectorization works:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "UafQPVGoMnm3", "outputId": "a8c018c2-b3ec-4dfc-9cca-06a7434015da"}, "outputs": [{"data": {"text/plain": ["array([[ 6,  7,  8,  9],\n", "       [10, 11, 12, 13],\n", "       [14, 15, 16, 17],\n", "       [18, 19, 20, 21],\n", "       [22, 23, 24, 25]])"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": [" # Integers from 1 to 20\n", "one_to_twenty = np.arange(1, 21)\n", "\n", "# Tranform to 5-row by 4-column matrix\n", "my_matrix = one_to_twenty.reshape(5, 4)\n", "\n", "# The following operations are done to every element of the matrix\n", "\n", "# Addition\n", "my_matrix + 5"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "wUfvwu50Mnm5"}, "outputs": [], "source": ["# Write your Python Code to display the result of the 2D matrix substract by 15\n", "# ...\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "XHNPXFIoMnm5", "outputId": "7feb24c1-8f5a-4f71-fed7-f523446ccfc2"}, "outputs": [{"data": {"text/plain": ["array([[  8,  16,  24,  32],\n", "       [ 40,  48,  56,  64],\n", "       [ 72,  80,  88,  96],\n", "       [104, 112, 120, 128],\n", "       [136, 144, 152, 160]])"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# Write your Python Code to display the result of the 2D matrix multiply by 8\n", "# ...\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "eTuW9mDEMnm5", "outputId": "0ce1dcf2-26fc-48ff-de9c-bff763f6721e"}, "outputs": [{"data": {"text/plain": ["array([[ 0.5,  1. ,  1.5,  2. ],\n", "       [ 2.5,  3. ,  3.5,  4. ],\n", "       [ 4.5,  5. ,  5.5,  6. ],\n", "       [ 6.5,  7. ,  7.5,  8. ],\n", "       [ 8.5,  9. ,  9.5, 10. ]])"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["# Division by 2\n", "my_matrix / 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "KP-ioTOCMnm6", "outputId": "2ee4de0d-5eec-4eb9-9b30-3d0c5d7006e0"}, "outputs": [{"data": {"text/plain": ["array([[  1,   4,   9,  16],\n", "       [ 25,  36,  49,  64],\n", "       [ 81, 100, 121, 144],\n", "       [169, 196, 225, 256],\n", "       [289, 324, 361, 400]], dtype=int32)"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["# Exponentiation (e.g. X^2)\n", "my_matrix ** 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "t6eQ5a0gMnm6", "outputId": "97ebce03-eb13-430b-fa79-bfbff826c448"}, "outputs": [{"data": {"text/plain": ["array([[      2,       4,       8,      16],\n", "       [     32,      64,     128,     256],\n", "       [    512,    1024,    2048,    4096],\n", "       [   8192,   16384,   32768,   65536],\n", "       [ 131072,  262144,  524288, 1048576]], dtype=int32)"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["# Powers of 2 (e.g. 2^X)\n", "2 ** my_matrix"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": true, "id": "51TVhPhTMnm7", "outputId": "9d13321e-1a78-447f-a721-f3c5193a178a"}, "outputs": [{"data": {"text/plain": ["array([[ 0.84147098,  0.90929743,  0.14112001, -0.7568025 ],\n", "       [-0.95892427, -0.2794155 ,  0.6569866 ,  0.98935825],\n", "       [ 0.41211849, -0.54402111, -0.99999021, -0.53657292],\n", "       [ 0.42016704,  0.99060736,  0.65028784, -0.28790332],\n", "       [-0.96139749, -0.75098725,  0.14987721,  0.91294525]])"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["# Trigonometry functions like sine\n", "np.sin(my_matrix)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "o8vZ-aV9MnnD", "outputId": "e603a4fa-ca60-4238-dd06-d70b5e528f61"}, "outputs": [{"data": {"text/plain": ["array([[0.76159416, 0.96402758, 0.99505475, 0.9993293 ],\n", "       [0.9999092 , 0.99998771, 0.99999834, 0.99999977],\n", "       [0.99999997, 1.        , 1.        , 1.        ],\n", "       [1.        , 1.        , 1.        , 1.        ],\n", "       [1.        , 1.        , 1.        , 1.        ]])"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["# Write your Python Code to display the result of the 2D matrix using hyperbolic tangent function\n", "# ...\n", "\n", "\n"]}, {"cell_type": "markdown", "metadata": {"id": "0IzM1t76MnnD"}, "source": ["Finally, let's take a look at some useful methods commonly used in data analysis:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "AP_310y6MnnE", "outputId": "877b518a-9131-4033-a75d-43a55bd9bc52"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Sum: 210\n", "Max:  20\n", "Min:  1\n", "Mean:  10.5\n", "std:  5.766281297335398\n"]}], "source": ["# Summation of all elements in the matrix\n", "print (\"Sum:\", my_matrix.sum())\n", "\n", "# Find the maximum element\n", "print (\"Max: \", my_matrix.max())\n", "\n", "# Find the minimum element\n", "print (\"Min: \", my_matrix.min())\n", "\n", "# Find the arithmetric mean\n", "print (\"Mean: \", my_matrix.mean())\n", "\n", "# Find the standard deviation\n", "print (\"std: \", my_matrix.std())"]}, {"cell_type": "markdown", "metadata": {"id": "qsAdF8OsMnnE"}, "source": ["To learn more about Nump<PERSON>, you may refer to the official quick start tutorial available at https://docs.scipy.org/doc/numpy/user/quickstart.html"]}, {"cell_type": "markdown", "metadata": {"id": "XH7jDC96MnnE"}, "source": ["***\n", "### Pandas\n", "\n", "Pandas was fundamentally created for working with two types of data structures. For one-dimensional data, you have a Series. The most common use of Pandas is the two-dimensional structure called the DataFrame. You can think of it as an Excel spreadsheet or an SQL table.\n", "\n", "![pandas-2.png](attachment:pandas-2.png)\n", "\n", "Although there are other data structures, with these two we can cover more than 90% of the use cases in AI. In fact, most of the time you will be working with DataFrames. If you are totally new to this library, you are recommended the 10 minutes to Pandas tutorial available at https://pandas.pydata.org/pandas-docs/stable/10min.html."]}, {"cell_type": "markdown", "metadata": {"id": "3PLWRfZZMnnF"}, "source": ["***\n", "### <PERSON><PERSON><PERSON><PERSON>b\n", "\n", "This is the main library for producing 2D visualizations. It is one of the oldest scientific computing tools in the Python ecosystem. Although there is an increasing number of libraries for visualization for Python, Matplotlib is still widely used and actually incorporated into the pandas functionality. In addition, other more specialized visualization projects such as Seaborn are based on Matplotlib. You may refer to https://matplotlib/org/ for additional information.\n", "\n", "You will need to be familar with some of the basic terminology and concepts of matplotlib because frequently you will need to make modifications to the objects and plots produced by your Python code. Let's introduce some of the basics you need to know about this library so that you can get started visualizing data. Let's import the library as is customary when working in analytics:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "UitvvdWfMnnF"}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "%matplotlib inline"]}, {"cell_type": "markdown", "metadata": {"id": "HAXC1AKgMnnF"}, "source": ["This is necessary for showing the figures in the Jupyter Notebook.\n", "\n", "First, you have two important objects - figures subplots (also known as axes). The diagram is the top-level container for all plot elements and is the container of subplot. One diagram can have many subplots and each subplot belongs to a single diagram. The following code produces a diagram (which is not seen) with a single empty subplot. Each subplot has many elements such as a x-axis, y-axis and a title:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "7_1JnwUCMnnG", "outputId": "491c63c7-4501-45f9-f2c2-8cd768cefbe6"}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots()\n", "ax.plot();"]}, {"cell_type": "markdown", "metadata": {"id": "a8zjcMXpMnnG"}, "source": ["A diagram with four subplots would be produced by the following code:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "xAzCWgShMnnG", "outputId": "bd4700cc-0598-4084-f3f1-944e70d05480"}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 4 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots(ncols=2, nrows=2)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {"id": "UpSSx-lOMnnH"}, "source": ["One important thing to know about matplotlib is that it can be confusing for the beginner because there are two ways (interfaces) of using it - Pyplot and the Object Oriented Interface (OOI). OOI is prefered because it makes explicit the object you are working with. The formerly produced axes object is a NumPy array containing the four subplots. Let's plot some random numbers just to show you how you can refer to each of the subplots."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "KYQ0jWHSMnnH", "outputId": "22c941c4-dad9-47f4-e657-f67d10de9d6b"}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 4 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["fig, axes = plt.subplots(ncols=2, nrows=2)\n", "axes[0, 0].set_title('upper left')\n", "axes[0, 0].plot(np.arange(10), np.random.randint(0, 10, 10))\n", "\n", "axes[0, 1].set_title('upper right')\n", "axes[0, 1].plot(np.arange(10), np.random.randint(0, 10, 10))\n", "\n", "axes[1, 0].set_title('lower left')\n", "axes[1, 0].plot(np.arange(10), np.random.randint(0, 10, 10))\n", "\n", "axes[1, 1].set_title('lower right')\n", "axes[1, 1].plot(np.arange(10), np.random.randint(0, 10, 10))\n", "\n", "# This statement is to get a nice spacing between the subplot\n", "fig.tight_layout();"]}, {"cell_type": "markdown", "metadata": {"id": "WAlq_cSTMnnI"}, "source": ["Since the axes object is a NumPy array, you refer to each of the subplots using the NumPy indexation, then you use methods such as .set_title() or .plot() on each subplot to modify it as we would like. There are many of those methods and most of them are used to modify elements of a subplot. For example, the following is almost the same code as before, but written in a way that is a bit more compact and modified the y-axis's tick marks.\n", "\n", "The other API, pyploy, is the one you will find in most of the online examples. This is the code to produce the above plots using pyplot:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "LVp1K2h_MnnJ", "outputId": "2dac95b9-da81-4516-f70b-9ab4a88d179e"}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 4 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["titles = ['upper left', 'upper right', 'lower left', 'lower right']\n", "fig, axes = plt.subplots(ncols=2, nrows=2)\n", "for title, ax in zip(titles, axes.flatten()):\n", "    ax.set_title(title)\n", "    ax.plot(np.arange(10), np.random.randint(0, 10, 10))\n", "    ax.set_yticks([0, 5, 10])\n", "fig.tight_layout();"]}, {"cell_type": "markdown", "metadata": {"id": "D0HXOImuMnnJ"}, "source": ["The following code is a minimal example of pyplot:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "0MtD6sC1MnnS", "outputId": "9da7b990-e875-42c4-8b28-5b71f521c2ab"}, "outputs": [{"data": {"text/plain": ["Text(0, 0.5, 'some numbers')"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# Fill the ? with any numerical value between 0 to 9\n", "plt.plot([?, ?, ?, ?])\n", "plt.title('Minimal pyplot example')\n", "plt.ylabel('some numbers')"]}, {"cell_type": "markdown", "metadata": {"id": "9be0FO7oMnnS"}, "source": ["***"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}, "colab": {"provenance": []}}, "nbformat": 4, "nbformat_minor": 0}