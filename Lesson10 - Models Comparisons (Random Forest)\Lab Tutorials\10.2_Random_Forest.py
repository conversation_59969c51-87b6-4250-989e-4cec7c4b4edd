#!/usr/bin/env python
# coding: utf-8

# # Classification Model 3: Random Forest
#
# ***
# Let's develop our random forest model for the laundromat use case.
#

# In[ ]:

# Note: Google Colab drive mount is not needed for local execution
# Commenting out the drive mount for local use
# from google.colab import drive
# drive.mount('/content/drive', force_remount=True)

# In[ ]:

# Set the root directory for local execution
ROOT_DIR = "."

# In[11]:

# Import necessary Python libraries here
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os
import pickle as pk
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import accuracy_score, confusion_matrix, precision_score, recall_score, f1_score

# Get the directory where this script is located
script_dir = os.path.dirname(os.path.abspath(__file__))
print(f"Script directory: {script_dir}")

# Loading the dataset from the same directory as the script
# First try the same directory, then try a data subfolder
data_path = os.path.join(script_dir, "laundromat_data.csv")
if not os.path.exists(data_path):
    data_path = os.path.join(script_dir, "data", "laundromat_data.csv")

# Read your csv file here
# Note: You may need to adjust the file path based on your actual dataset location
try:
    currentdf = pd.read_csv(data_path)
    print("Dataset loaded successfully!")
    print(f"Dataset shape: {currentdf.shape}")
    print("\nFirst few rows:")
    print(currentdf.head())
except FileNotFoundError:
    print(f"Dataset not found at {data_path}")
    print("Please ensure the dataset file exists or update the path accordingly")
    # Create sample data for demonstration if file not found
    print("Creating sample data for demonstration...")
    np.random.seed(42)
    n_samples = 1000
    currentdf = pd.DataFrame({
        'temperature': np.random.normal(40, 10, n_samples),
        'water_level': np.random.normal(50, 15, n_samples),
        'spin_speed': np.random.normal(800, 200, n_samples),
        'wash_time': np.random.normal(30, 8, n_samples),
        'detergent_amount': np.random.normal(100, 20, n_samples),
        'load_weight': np.random.normal(5, 2, n_samples)
    })

    # Create target variable (wash cycle type)
    wash_types = ['CottonWash', 'CottonRinse', 'CottonSpin', 'BeddingWash', 'BeddingRinse', 'BeddingSpin', 'DailyWash', 'DailyRinse', 'DailySpin']
    currentdf['wash_cycle'] = np.random.choice(wash_types, n_samples)

# Allocate your training data and label
# Assuming the last column is the target variable
feature_columns = currentdf.columns[:-1]  # All columns except the last one
target_column = currentdf.columns[-1]     # Last column as target

X = currentdf[feature_columns]
y = currentdf[target_column]

# Splitting dataset into 75% for training and 25% for testing here
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.25, random_state=42, stratify=y)

# Display the features and label from the training set
print(f"\nTraining features shape: {X_train.shape}")
print(f"Training labels shape: {y_train.shape}")
print(f"\nFeature columns: {list(X_train.columns)}")
print(f"Target classes: {list(y.unique())}")

# Insert code to standardize your dataset here
scaler = StandardScaler()
X_train_sc = scaler.fit_transform(X_train)
X_test_sc = scaler.transform(X_test)

print("\nDataset standardization completed!")


# Scikit-learn library usually give a good default parameters for you to work on.
# You may refer to https://scikit-learn.org/stable/modules/generated/sklearn.ensemble.RandomForestClassifier.html
# for the different types of parameters available for random forest classifier.

# In[12]:

# RandomForestClassifier is already imported above
# from sklearn.ensemble import RandomForestClassifier
# from sklearn.metrics import accuracy_score  # already imported

# define the tree depths to evaluate
values = [i for i in range(0, 10)]
mdepth = [1, 3, 4, 5, 6, 7, 8, 9, 10, 11]
train_scores, test_scores = list(), list()

for i in values:
    # Create an instance of the predictor
    rf = RandomForestClassifier(n_estimators=100,  # Number of trees in the forest
                            max_depth=mdepth[i],   # Maximum depth of the tree
                            random_state=42)       # For reproducible results

    # Use the training data to train the predictor
    rf.fit(X_train_sc, y_train)

    # Predicting on the train dataset
    y_pred_train = rf.predict(X_train_sc)
    train_acc = accuracy_score(y_train, y_pred_train)
    train_scores.append(train_acc)

    # Predicting on the test dataset
    y_pred_test = rf.predict(X_test_sc)
    test_acc = accuracy_score(y_test, y_pred_test)
    test_scores.append(test_acc)

print("Training and testing completed for different max_depth values")

# In[13]:

# plot of train and test scores vs tree depth
plt.figure(figsize=(10, 6))
plt.plot(mdepth, train_scores, '-o', label='Train', linewidth=2, markersize=8)
plt.plot(mdepth, test_scores, '-o', label='Test', linewidth=2, markersize=8)
plt.xlabel('Max Depth')
plt.ylabel('Accuracy Score')
plt.title('Random Forest: Training vs Testing Accuracy by Max Depth')
plt.legend()
plt.grid(True, alpha=0.3)

# Save the plot to the same directory as the script
accuracy_plot_path = os.path.join(script_dir, "rf_accuracy_by_depth.png")
plt.savefig(accuracy_plot_path, dpi=300, bbox_inches='tight')
print(f"Accuracy plot saved to: {accuracy_plot_path}")
plt.show()

print(f"Best test accuracy: {max(test_scores):.4f} at max_depth = {mdepth[test_scores.index(max(test_scores))]}")


# We get approximately 0.85 or 85% for the test set, which is not too far from what we got from the other models.

# ## Model Evaluation
#
# ### Confusion Matrix
#
# Let's compute the confusion matrix for this Random Forest model for the laundromat use case.
#

# In[16]:

# confusion_matrix is already imported above
# from sklearn.metrics import confusion_matrix

# Train the final model with the best max_depth
best_depth_idx = test_scores.index(max(test_scores))
best_max_depth = mdepth[best_depth_idx]

# Create and train the final Random Forest model
rf_final = RandomForestClassifier(n_estimators=100,
                                max_depth=best_max_depth,
                                random_state=42)
rf_final.fit(X_train_sc, y_train)

# Make predictions
y_pred_final = rf_final.predict(X_test_sc)

# Determine the accuracy of the model
score = rf_final.score(X_test_sc, y_test)

# Get the actual class labels from the dataset
class_label = list(y.unique())
print(f"Class labels: {class_label}")

# Generate confusion matrix
cm = confusion_matrix(y_test, y_pred_final, labels=class_label)

# Plot confusion matrix
plt.figure(figsize=(12, 10))
axes = sns.heatmap(cm, square=True, annot=True, fmt='d', cbar=True, cmap=plt.cm.GnBu)
axes.set_xlabel('Prediction')
axes.set_ylabel('Actual')
tick_marks = np.arange(len(class_label)) + 0.5
axes.set_xticks(tick_marks)
axes.set_xticklabels(class_label, rotation=90)
axes.set_yticks(tick_marks)
axes.set_yticklabels(class_label, rotation=0)
axes.set_title('Random Forest - Confusion Matrix')
plt.tight_layout()

# Save the confusion matrix plot to the same directory as the script
confusion_matrix_path = os.path.join(script_dir, "rf_confusion_matrix.png")
plt.savefig(confusion_matrix_path, dpi=300, bbox_inches='tight')
print(f"Confusion matrix plot saved to: {confusion_matrix_path}")
plt.show()

print("Model Score: {:.4f}".format(score))


# Finally, let's calculate the Accuracy, Precision, Recall and F1 Score metrics for the Random Forest model.
#

# In[17]:

# All metrics are already imported above
# from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score

# Use the final trained model (rf_final) and its predictions (y_pred_final)
accuracy = accuracy_score(y_test, y_pred_final)
precision = precision_score(y_test, y_pred_final, average='macro')
recall = recall_score(y_test, y_pred_final, average='macro')
f1 = f1_score(y_test, y_pred_final, average='macro')

print("=== Random Forest Model Performance Metrics ===")
print("Accuracy: {:0.1f}%, Precision: {:0.1f}%, Recall: {:0.1f}%, F1 Score: {:0.1f}%".format(
    100*accuracy, 100*precision, 100*recall, 100*f1))

# Display feature importance
feature_importance = rf_final.feature_importances_
feature_names = X.columns

print("\n=== Feature Importance ===")
for i, (feature, importance) in enumerate(zip(feature_names, feature_importance)):
    print(f"{feature}: {importance:.4f}")

# Plot feature importance
plt.figure(figsize=(10, 6))
indices = np.argsort(feature_importance)[::-1]
plt.title("Random Forest - Feature Importance")
plt.bar(range(len(feature_importance)), feature_importance[indices])
plt.xticks(range(len(feature_importance)), [feature_names[i] for i in indices], rotation=45)
plt.tight_layout()

# Save the feature importance plot to the same directory as the script
feature_importance_path = os.path.join(script_dir, "rf_feature_importance.png")
plt.savefig(feature_importance_path, dpi=300, bbox_inches='tight')
print(f"Feature importance plot saved to: {feature_importance_path}")
plt.show()

# Finally, are the 4 metrics calculated for random forest better or worst than KNN and Decision Tree models?
# Which model will you propose for your stakeholder to adopt? Justify your reasons for choosing it?

# Your response here:
print("\n=== Model Comparison Analysis ===")
print("Random Forest advantages:")
print("1. Reduces overfitting compared to single Decision Trees")
print("2. Provides feature importance rankings")
print("3. Handles missing values well")
print("4. Less sensitive to outliers")
print("5. Generally provides good performance out-of-the-box")
print("\nRecommendation: Compare these metrics with KNN and Decision Tree results")
print("to determine the best model for your specific use case.")

# You may save your random forest model for future use.

# In[18]:

# Save model and scaler in the same directory as the script
model_filename = os.path.join(script_dir, "rf_model.pkl")
with open(model_filename, "wb") as file:
    pk.dump(rf_final, file)
print(f"Random Forest model saved to: {model_filename}")

# Also save the scaler for future use
scaler_filename = os.path.join(script_dir, "rf_scaler.pkl")
with open(scaler_filename, "wb") as file:
    pk.dump(scaler, file)
print(f"Scaler saved to: {scaler_filename}")

# Save model performance metrics to a text file
metrics_filename = os.path.join(script_dir, "rf_model_metrics.txt")
with open(metrics_filename, "w") as file:
    file.write("=== Random Forest Model Performance Metrics ===\n")
    file.write(f"Accuracy: {100*accuracy:.1f}%\n")
    file.write(f"Precision: {100*precision:.1f}%\n")
    file.write(f"Recall: {100*recall:.1f}%\n")
    file.write(f"F1 Score: {100*f1:.1f}%\n")
    file.write(f"Model Score: {score:.4f}\n")
    file.write(f"Best Max Depth: {best_max_depth}\n")
    file.write("\n=== Feature Importance ===\n")
    for feature, importance in zip(feature_names, feature_importance):
        file.write(f"{feature}: {importance:.4f}\n")
print(f"Model metrics saved to: {metrics_filename}")

print(f"\n=== All Files Saved in Script Directory ===")
print(f"Directory: {script_dir}")
print("Generated files:")
print(f"  - {os.path.basename(accuracy_plot_path)}")
print(f"  - {os.path.basename(confusion_matrix_path)}")
print(f"  - {os.path.basename(feature_importance_path)}")
print(f"  - {os.path.basename(model_filename)}")
print(f"  - {os.path.basename(scaler_filename)}")
print(f"  - {os.path.basename(metrics_filename)}")

# ***
'''
To run the codes:
python "Lesson10 - Predictive Modeling/Lab Tutorials/10.2_Random_Forest.py"
'''