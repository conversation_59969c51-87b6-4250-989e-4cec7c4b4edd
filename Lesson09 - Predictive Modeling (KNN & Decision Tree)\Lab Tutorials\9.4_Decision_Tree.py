#!/usr/bin/env python
# coding: utf-8

# # Classification Model 2: Decision Tree
#
# ***
# ### Loading Dataset

# In[1]:

# Note: Google Colab imports removed for local execution
# from google.colab import drive
# drive.mount('/content/drive', force_remount=True)

# In[2]:

# Updated for local environment
import os
ROOT_DIR = os.path.dirname(os.path.abspath(__file__))
print(f"Working directory: {ROOT_DIR}")
print(f"All graphics will be saved in: {ROOT_DIR}")

# In[3]:

# Import necessary Python libraries here
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.tree import DecisionTreeClassifier
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix
from sklearn.tree import export_graphviz
import pickle as pk
from io import StringIO

# For tree visualization (alternative to pydotplus for local environment)
from sklearn import tree

# Loading the dataset from the /data folder here
data_path = os.path.join(ROOT_DIR, "data", "CleandDataV20210515.csv")

# Read your csv file here
currentdf = pd.read_csv(data_path)
print("Dataset loaded successfully!")
print(f"Dataset shape: {currentdf.shape}")
print("\nFirst few rows:")
print(currentdf.head())

# Allocate your training data and label
x = currentdf.drop(['Unnamed: 0', 'mode', 'index_col'], axis=1)  # Features (excluding index and target columns)
y = currentdf['mode']  # Target variable (mode)

print(f"\nFeatures: {list(x.columns)}")
print(f"Target classes: {y.unique()}")

# Splitting dataset into 75% for training and 25% for testing here
X_train, X_test, y_train, y_test = train_test_split(x, y, test_size=0.25, random_state=42)

# Display the features and label from the training set
print("Features shape:", X_train.shape)
print("Labels shape:", y_train.shape)

# Insert code to standardize your dataset here
scaler = StandardScaler()
X_train_scaled = scaler.fit_transform(X_train)
X_test_scaled = scaler.transform(X_test)

print("Dataset standardized successfully!")


# ## Building a Decision Tree Model
#
# Let's train a model using the selected features extracted earlier and set the 'max_depth' parameter to 3.
#

# In[4]:

# Build the initial Decision Tree model
class_tree = DecisionTreeClassifier(max_depth=3, random_state=42)
class_tree.fit(X_train_scaled, y_train)
print("Decision Tree model trained successfully!")

# We visualize the classification tree using matplotlib (works in local environment)
# This replaces the pydotplus/graphviz approach used in Colab

# In[5]:

# Display decision tree using matplotlib (works in local environment)
plt.figure(figsize=(20, 10))
tree.plot_tree(class_tree,
               feature_names=X_train.columns,
               class_names=['BeddingRinse', 'BeddingSpin', 'BeddingWash', 'CottonRinse', 'CottonSpin', 'CottonWash',
                           'DailyRinse', 'DailySpin', 'DailyWash'],
               rounded=True,
               filled=True)
plt.title("Decision Tree Visualization (max_depth=3)")
# Save graphics in the same directory as the script
output_path = os.path.join(ROOT_DIR, "decision_tree_depth3.png")
plt.savefig(output_path, dpi=300, bbox_inches='tight')
print(f"Decision tree visualization saved as '{output_path}'")
plt.show()

# Next, print the 'proportions' by setting the parameter for proportion=True.

# In[6]:

# Display decision tree with proportions using matplotlib
plt.figure(figsize=(20, 10))
tree.plot_tree(class_tree,
               feature_names=X_train.columns,
               class_names=['BeddingRinse', 'BeddingSpin', 'BeddingWash', 'CottonRinse', 'CottonSpin', 'CottonWash',
                           'DailyRinse', 'DailySpin', 'DailyWash'],
               rounded=True,
               filled=True,
               proportion=True)
plt.title("Decision Tree Visualization with Proportions (max_depth=3)")
# Save graphics in the same directory as the script
output_path = os.path.join(ROOT_DIR, "decision_tree_depth3_proportions.png")
plt.savefig(output_path, dpi=300, bbox_inches='tight')
print(f"Decision tree with proportions saved as '{output_path}'")
plt.show()

# Has the tree structure changed? Are you getting a different result now? Explain your observation.
print("\nObservation:")
print("The tree structure remains the same, but the display shows proportions instead of counts.")
print("This makes it easier to understand the relative distribution of classes in each node.")
print("Proportions help in understanding the purity of each split and the confidence of predictions.")

# ## Training a Large Classification Tree
#
# Let's train a larger tree by modifying the 'max_depth' to 6 and 'min_samples_split' to 50 in the scikit-learn parameters. You may also refer to https://scikit-learn.org/stable/modules/generated/sklearn.tree.DecisionTreeClassifier.html for other types of parameters.

# In[7]:

# Train a larger decision tree
class_tree = DecisionTreeClassifier(max_depth=6, min_samples_split=50, random_state=42)
class_tree.fit(X_train_scaled, y_train)
y_pred_class_tree = class_tree.predict(X_test_scaled)

print("Larger Decision Tree model trained successfully!")

# We can calculate the training accuracy score of this model as follows:

# In[8]:

# Calculate accuracy
accuracy_class_tree = accuracy_score(y_true=y_test, y_pred=y_pred_class_tree)
print(f"Decision Tree Accuracy: {accuracy_class_tree:.4f} ({accuracy_class_tree*100:.1f}%)")

# We get approximately 89.1% accuracy for this Decision Tree model, which is much higher than the KNN model (81.4%).

# Another nice feature of this model is that we can get a normalized score of "important" features by using the 'feature_importances_' method.

# In[9]:

# Display feature importances
feature_importance = pd.Series(data=class_tree.feature_importances_,
                              index=X_train.columns).sort_values(ascending=False).round(3)
print("Feature Importances:")
print(feature_importance)

# You can also compare these values using a bar graph.

# In[10]:

# Plot feature importances
plt.figure(figsize=(12, 8))
feature_importance.plot(kind='bar')
plt.title('Feature Importance in Decision Tree Model')
plt.xlabel('Features')
plt.ylabel('Importance Score')
plt.xticks(rotation=45)
plt.tight_layout()
# Save graphics in the same directory as the script
output_path = os.path.join(ROOT_DIR, "feature_importance.png")
plt.savefig(output_path, dpi=300, bbox_inches='tight')
print(f"Feature importance plot saved as '{output_path}'")
plt.show()

# Discuss with your team what can you tell from these feature importance?
print("\nFeature Importance Analysis:")
print("The feature importance scores show which variables are most influential in the decision tree.")
print("Higher scores indicate features that contribute more to the model's decision-making process.")
print("This helps identify the most critical measurements for classifying washing machine cycles.")

# ## Model Evaluation
#
# ### Confusion Matrix
#
# Let's compute the confusion matrix for this Decision Tree model for the laundromat use case.
#

# In[11]:

# Determine the accuracy of the model using test data
score = class_tree.score(X_test_scaled, y_test)
print(f"Model accuracy on test data: {score:.4f}")

# Provide the necessary labels (sorted alphabetically as they appear in the dataset)
class_label = sorted(y.unique())
print(f"Class labels: {class_label}")

# Generate confusion matrix
cm = confusion_matrix(y_test, y_pred_class_tree, labels=class_label)

# Plot confusion matrix
plt.figure(figsize=(12, 10))
axes = sns.heatmap(cm, square=True, annot=True, fmt='d', cbar=True, cmap=plt.cm.Blues)
axes.set_xlabel('Predicted')
axes.set_ylabel('Actual')
tick_marks = np.arange(len(class_label))
axes.set_xticks(tick_marks + 0.5)
axes.set_xticklabels(class_label, rotation=45)
axes.set_yticks(tick_marks + 0.5)
axes.set_yticklabels(class_label, rotation=0)
axes.set_title('Confusion Matrix - Decision Tree Model')
plt.tight_layout()
# Save graphics in the same directory as the script
output_path = os.path.join(ROOT_DIR, "confusion_matrix.png")
plt.savefig(output_path, dpi=300, bbox_inches='tight')
print(f"Confusion matrix saved as '{output_path}'")
plt.show()

# Let's calculate the accuracy, precision, recall and F1 Score for our Decision Tree model using the test dataset.

# In[12]:

# Calculate comprehensive metrics
accuracy = accuracy_score(y_test, y_pred_class_tree)
precision = precision_score(y_test, y_pred_class_tree, average='macro')
recall = recall_score(y_test, y_pred_class_tree, average='macro')
f1 = f1_score(y_test, y_pred_class_tree, average='macro')

print("Decision Tree Model Performance:")
print("=" * 40)
print(f"Accuracy:  {accuracy*100:.1f}%")
print(f"Precision: {precision*100:.1f}%")
print(f"Recall:    {recall*100:.1f}%")
print(f"F1 Score:  {f1*100:.1f}%")

# Are the accuracy, precision, recall and F1 Score for Decision Tree model better or worse than your KNN model? Explain.
print("\nComparison with KNN Model:")
print("The Decision Tree model typically achieves around 89.1% accuracy, which is significantly")
print("higher than the KNN model's 81.4% accuracy. This improvement is due to the Decision Tree's")
print("ability to capture non-linear relationships and create more complex decision boundaries.")

# Lastly, you may save your Decision Tree model for future comparison.

# In[13]:

# Create model directory if it doesn't exist
model_dir = os.path.join(ROOT_DIR, "model")
os.makedirs(model_dir, exist_ok=True)

# Save the trained model
model_filename = os.path.join(model_dir, "dt.mdl")
with open(model_filename, "wb") as file:
    pk.dump(class_tree, file)
print(f"Decision Tree model saved as '{model_filename}'")


print("\n" + "="*60)
print("DECISION TREE CLASSIFICATION TUTORIAL COMPLETED")
print("="*60)
print("Summary:")
print("- Dataset loaded and preprocessed successfully")
print("- Decision Tree model trained with max_depth=6")
print("- Model visualizations saved as PNG files")
print("- Feature importance analysis completed")
print("- Confusion matrix generated")
print("- Model performance metrics calculated")
print("- Trained model saved for future use")
print("="*60)

'''
# Run the main script
python "Lesson09 - Analysis Models\Lab Tutorials\9.4_Decision_Tree.py"

# Verify graphics location
python "Lesson09 - Analysis Models\Lab Tutorials\verify_graphics_location.py"
'''