{"cells": [{"cell_type": "markdown", "id": "81d187d1", "metadata": {"id": "81d187d1"}, "source": ["# Classification Model 1: KNN\n", "\n", "***\n", "## Loading Dataset\n"]}, {"cell_type": "code", "execution_count": 1, "id": "22ccd564", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "22ccd564", "executionInfo": {"status": "ok", "timestamp": 1749716670399, "user_tz": -480, "elapsed": 27751, "user": {"displayName": "<PERSON>", "userId": "08280565725279349310"}}, "outputId": "95c819ce-acd9-45d3-9ff9-dc9642599652"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Mounted at /content/drive\n"]}], "source": ["from google.colab import drive\n", "\n", "drive.mount('/content/drive', force_remount=True)"]}, {"cell_type": "code", "execution_count": 2, "id": "0c2f2a10", "metadata": {"id": "0c2f2a10", "executionInfo": {"status": "ok", "timestamp": 1749716675630, "user_tz": -480, "elapsed": 6, "user": {"displayName": "<PERSON>", "userId": "08280565725279349310"}}}, "outputs": [], "source": ["ROOT_DIR = '/content/drive/MyDrive/C3790C'"]}, {"cell_type": "code", "execution_count": null, "id": "7a4a8a70", "metadata": {"id": "7a4a8a70"}, "outputs": [], "source": ["# Import necessary Python libraries here ...\n", "# ...\n", "# ...\n"]}, {"cell_type": "markdown", "id": "a770d865", "metadata": {"id": "a770d865"}, "source": ["### Data Preparation"]}, {"cell_type": "code", "execution_count": null, "id": "771285eb", "metadata": {"id": "771285eb", "outputId": "bb04cf4e-d86b-4b85-f0a3-d1ae76b8a676"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["     index_col       avC         avP       sdC         sdP          avR  maxC  \\\n", "432          3  3.595600  792.930000  3.332669  786.913383   399.232039  7.23   \n", "22           9  1.616815  329.370370  2.485054  583.668067   637.157143  1.76   \n", "620         14  1.305465  254.702326  2.232888  529.072646   604.123431  0.66   \n", "963          0  0.284667   34.533333  0.227744   33.000244  1429.358887  0.77   \n", "188          9  1.484593  298.003704  2.357156  558.706609   623.840883  1.84   \n", "..         ...       ...         ...       ...         ...          ...   ...   \n", "360         21  0.906095  167.071429  1.721403  405.103331   871.333092  0.09   \n", "466         15  1.292000  256.215556  2.277856  534.821437   658.162380  1.30   \n", "299          0  2.612000  574.333333  3.216506  759.213038   753.064179  7.25   \n", "493         20  1.080317  202.135000  1.919027  450.266332   678.029294  2.49   \n", "527          9  1.641481  339.711111  2.584596  619.005346   585.429776  0.59   \n", "\n", "     maxP     stdCR       stdCP         AvRR  \n", "432  1642  3.382349  805.313577   362.408577  \n", "22    271  0.587681   92.810374   449.344412  \n", "620    69  0.141624   18.216104   682.374211  \n", "963   102  0.227744   33.000244  1429.358887  \n", "188   286  0.601370   96.551553   475.380790  \n", "..    ...       ...         ...          ...  \n", "360     1  0.000000    0.000000  2628.307407  \n", "466   195  0.325882   50.023153   638.702682  \n", "299  1666  3.216506  759.213038   753.064179  \n", "493   395  0.630406  102.162728   226.295845  \n", "527    75  0.109605   15.296908   559.013457  \n", "\n", "[773 rows x 11 columns]\n", "432     CottonWash\n", "22      DailyRinse\n", "620    CottonRinse\n", "963    BeddingWash\n", "188     DailyRinse\n", "          ...     \n", "360     CottonSpin\n", "466    CottonRinse\n", "299     CottonWash\n", "493     CottonSpin\n", "527     CottonWash\n", "Name: mode, Length: 773, dtype: object\n"]}], "source": ["# Loading the dataset from the /data folder here\n", "data_path  = ?\n", "\n", "# Read your csv file here ...\n", "currentdf  = ???\n", "\n", "# Allocate your training data and label\n", "x = ?\n", "y = ?\n", "\n", "# Splitting dataset into 75% for training and 25% for testing here ...\n", "# ...\n", "# ...\n", "\n", "# Display the features and label from the training set\n", "print(?)\n", "print(?)\n", "\n", "# Insert code to standardize your dataset here ...\n", "# ...\n", "# ...\n"]}, {"cell_type": "markdown", "id": "90849665", "metadata": {"id": "90849665"}, "source": ["## Building a KNN Model\n", "\n", "Let's train a model using the selected features extracted earlier."]}, {"cell_type": "code", "execution_count": null, "id": "79922898", "metadata": {"id": "79922898"}, "outputs": [], "source": ["from sklearn.neighbors import KNeighborsClassifier\n", "from sklearn.metrics import accuracy_score\n", "\n", "train_scores, test_scores = list(), list()\n", "\n", "# define the tree depths to evaluate later\n", "values = [i for i in range(0, 10)]\n", "mdepth=[3, 5, 7, 9, 10, 11, 12, 13, 15, 17]\n", "\n", "for i in values:\n", "    knn = KNeighborsClassifier(n_neighbors=mdepth[i])\n", "    knn.fit(X_train_sc, y_train)\n", "\n", "    # Predicting on the train dataset\n", "    y_pred_train = knn.predict(X_train_sc)\n", "    train_acc = accuracy_score(y_train, y_pred_train)\n", "    train_scores.append(train_acc)\n", "\n", "    # Predicting on the test dataset\n", "    y_pred_test = knn.predict(X_test_sc)\n", "    test_acc = accuracy_score(y_test, y_pred_test)\n", "    test_scores.append(test_acc)\n", ""]}, {"cell_type": "markdown", "id": "1cd8ade0", "metadata": {"id": "1cd8ade0"}, "source": ["The model has been trained using all the features we have in X_train.\n", "\n", "After we have trained our first predictive model, we compute the first performance metric - Accuracy to evaluate our KNN model. Accuracy is defined as the proportion (or percentage) of predictions that are correct. Let's plot and compare the accuracy of the trained and test dataset for 10 iterations."]}, {"cell_type": "code", "execution_count": null, "id": "5b86c883", "metadata": {"scrolled": true, "id": "5b86c883", "outputId": "00c9bf1d-a7b3-47a6-e3ba-2810970a2624"}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAXoAAAD4CAYAAADiry33AAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjMuNCwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8QVMy6AAAACXBIWXMAAAsTAAALEwEAmpwYAAApO0lEQVR4nO3deXhU5d3/8fc3k4UAIWFHkkCwYsIimwFFtArU4l5qW7dqlWorPu6tuPWp9antU39qtbXa+lhR6+VWRESs1g20iCiyhEUEBAOEsIYlYQtku39/nARCMpAJTHKSk8/rurwgZ2Yy3xnJJ9+5z33u25xziIhIcMX4XYCIiDQsBb2ISMAp6EVEAk5BLyIScAp6EZGAi/W7gHA6derkMjIy/C5DRKTZmD9//lbnXOdwtzXJoM/IyGDevHl+lyEi0myY2drD3aahGxGRgFPQi4gEnIJeRCTgmuQYvYhIfZSWlpKfn8++ffv8LqXBtWrVirS0NOLi4iJ+jIJeRJq9/Px8kpKSyMjIwMz8LqfBOOfYtm0b+fn59OrVK+LHBSbop+as5+H3VrChsJjuKYlMGJPJ2MGpfpclIo1g3759gQ95ADOjY8eOFBQU1OtxgQj6qTnruWfKEopLywFYX1jMPVOWACjsRVqIoId8laN5nYE4GfvweysOhHyV4tJyHn5vhU8ViYg0HYEI+g2FxWGPry8s5okZK5m9ait79pc1clUi0lJs27aNQYMGMWjQILp160ZqauqBr0tKSo742Hnz5nHLLbc0aH2BGLrpnpLI+jBhHxtjPPL+1wDEGGR2a8eQHikM6dGewT1S6NWpTYv5uCciB0X7nF7Hjh1ZuHAhAPfffz9t27bljjvuOHB7WVkZsbHh4zY7O5vs7Oyjfu5IBCLoJ4zJPGSMHiAxLsQfLj6JkZldyFm3gwV5heTk7WDawg28NCcPgPat4xjco/2B8B+YnkKbhEC8JSJyGI11Tu+aa66hQ4cO5OTkMGTIEC699FJuu+02iouLSUxM5LnnniMzM5OPP/6YRx55hH/961/cf//95OXlkZubS15eHrfddltUuv1ApFrV/5zD/YY+K7MLZ2V2AaC8wrFqy24W5O1gwdodLMjbwYzlW4DaXf+Qnu3J6NhaXb9IM/I/by3lqw07D3t7Tl4hJeUVhxwrLi3nzsmLeeWLvLCP6du9Hb+5sF+9a/n666/58MMPCYVC7Ny5k5kzZxIbG8uHH37Ivffey+uvv17rMcuXL+ejjz5i165dZGZmcsMNN9Rrznw4gQh68MI+kt/GoRgjs1sSmd2SuHxYDwAK95aQs66QnLVe5/9mta6/Q5t4BqenMKSnN9wzME1dv0hzVjPk6zp+LH70ox8RCoUAKCoq4uqrr2blypWYGaWlpWEfc/7555OQkEBCQgJdunRh8+bNpKWlHVMdSiwgpXU8IzO7MLJa179yyy4WrC1kQd4OcvJ2ML1a15/VrR1DelZ2/T3a07Na16/5/CL+qqvzHvHgjLDn9FJTEvnn9cOjWkubNm0O/P3Xv/41I0eO5I033mDNmjWcddZZYR+TkJBw4O+hUIiysmOfSKKgDyMUY2R1a0dWt3ZccUr4rn9qzgZe/Nzr+ju2iWdwjxQSYmP4YNkWSsq8zkDz+UWansOd05swJrNBn7eoqIjUVC8Hnn/++QZ9rpoU9BGqq+tfkLeD3II9tR5XNZ9fQS/SNNR1Tq+h3HnnnVx99dU8+uijjBo1qkGfqyZzzjXqE0YiOzvbNceNR3rd/Tbh3k0DVj94fmOXI9JiLFu2jD59+vhdRqMJ93rNbL5zLuw8zUBcMNVUdE9JDHu8S7uEsMdFRBqDgj6KJozJJDEuVOv47n1lfLF6uw8ViYgo6KNq7OBU/nDxSaSmJGJ4Z/HvOTeLru1a8eNnPj/sHF0RkYakk7FRFm4+/2XDenDLKzncM2UJyzbu5NcX9CUupN+xItI4lDaNIDkxjmevGcr13z6eFz5by1UT57B9z5EXOhIRiRYFfSMJxRj3nNeHxy4dyIK8Qi56YhbLNh7+Mm0RkWhR0Dey7w9OY9L1wyktr+Div87m30s2+l2SiByjY1mmGODjjz9m9uzZDVafgt4Hg9JTeOum08k6LokbXlrAYx98TUVF07ueQSSwFk+Cx/rD/Snen4snHdO3q1qmeOHChYwfP57bb7/9wNfx8fF1Pl5BH1Bd2rXilZ+dyg9PTuPP01dyw0vztTmKSGNYPAneugWK1gHO+/OtW4457GuaP38+Z555JieffDJjxoxh40bv0/vjjz9O3759GTBgAJdddhlr1qzhqaee4rHHHmPQoEF88sknUa0DNOvGV63iQjz8wwH0Oa4dv3/7Ky7+62z+/pNsenRs7XdpIs3Xv++GTUsOf3v+XCjff+ix0mJ48yaY/4/wj+l2Epz7YMQlOOe4+eabefPNN+ncuTP//Oc/+dWvfsWzzz7Lgw8+yOrVq0lISKCwsJCUlBTGjx9fa7OSaFLQ+8zMuPb0XpzYtS03vZzDRU/O4q9XDOG0Ezr5XZpIMNUM+bqOH4X9+/fz5ZdfcvbZZ3vfuryc4447DoABAwbw4x//mLFjxzJ27NioPeeRRBT0ZnYO8GcgBDzjnHuwxu3tgWeBbwH7gJ86576M5LHiOaN3Z968cQTXvTCPq579gvsu6MtPhvfUpici9VVX5/1Y/8phmxqS02Hc21EpwTlHv379+Oyzz2rd9vbbbzNz5kymTZvGAw88wNKlS6PynEdS5xi9mYWAJ4Fzgb7A5WbWt8bd7gUWOucGAD/BC/ZIHyuVMjq14Y3/Oo2RmZ35zbSl3DNlyYElj0UkSkbfB3E11qWKS/SOR0lCQgIFBQUHgr60tJSlS5dSUVHBunXrGDlyJA899BCFhYXs3r2bpKQkdu3aFbXnrymSk7HDgFXOuVznXAnwKvC9GvfpC0wHcM4tBzLMrGuEj5VqklrF8fRV2dw08gRenbuOK/7+OQW7oveRUqTFG3AJXPi418Fj3p8XPu4dj5KYmBgmT57MXXfdxcCBAxk0aBCzZ8+mvLycK6+8kpNOOonBgwdz++23k5KSwoUXXsgbb7zh68nYVKD655x84JQa91kEXAzMMrNhQE8gLcLHSg0xMcYdYzLJOi6JO15bxEVPzOLpq7I5KS3Z79JEgmHAJVEN9uruv//+A3+fOXNmrdtnzZpV69iJJ57I4sWLG6QeiKyjDzdIXHPS94NAezNbCNwM5ABlET7WexKzn5vZPDObV1BQEEFZwXfBgO5MHn8aBvzwqdlMW7TB75JEpBmKJOjzgfRqX6cBhySOc26nc26cc24Q3hh9Z2B1JI+t9j2eds5lO+eyO3fuHPkrCLj+qclMu/l0BqQlc8srOfy/d5dTrourRKQeIgn6uUBvM+tlZvHAZcC06ncws5TK2wCuA2Y653ZG8lipW6e2Cbx03alcPqwHf/v4G372wjx27gu/g7xIS9UUd8trCEfzOusMeudcGXAT8B6wDJjknFtqZuPNbHzl3foAS81sOd4Mm1uP9Nh6VynEx8bwh4tP4oGx/Zn5dQHff/JTVm+tvUetSEvUqlUrtm3bFviwd86xbds2WrVqVa/Hac/YZujz3G3c8OJ8yiscT1wxhG+fqKEuadlKS0vJz89n3759fpfS4Fq1akVaWhpxcXGHHD/SnrEK+mZq3fa9/OyFeXy9eRf3nteHa0/vpYurRFowbQ4eQOkdWvP6Dacxpl83fvf2Mn752iL2lZb7XZaINEEK+masTUIsT14xhF+cfSJTFqzn0qc/Z/PO4H90FZH60aJmzVxMjHHL6N5kdkviF/9cyIV/mcUVp/TgtXn5bCgspntKIhPGZNbax7YxTM1Zz8PvrfC9DpGWTh19QIzp140p/zWCsooK/vThStYXFuOA9YXF3DNlCVNz1jdqPVNz1nPPlCW+1yEi6ugDJbNbEgmxIeDQOfbFpeXcP21po47hP/jv5RTXeL7i0nIefm+FunqRRqagD5hNReHH6AuLS7l7yhE2Y2gkGwqL/S5BpMVR0AdM95RE1ocJ027tEnjjxhGNVsf3n/yUTTvDr7r5mze/ZNyIXmR0atNo9Yi0ZAr6gJkwJpN7piw5ZNgkMS7E3ef24bjkxCM8MrruPrdPrToSYmMYkJrMy1/k8cLnazm7T1euO+N4hma01zUAIg1IQR8wVePffs92OVIdW3bu44XP1vLinLW8/9VmBqQlc+3pvTjvpOOIC2l+gEi06cpY8U1xSTmvL8jn2Vmryd26h+OSW3HNaRlcNqwHyYlxdX8DETlASyBIk1ZR4fhoxRae+WQ1n+Vuo3V8iEuy0/npiF706Nja7/KiRtcVSENS0Euz8eX6Ip6dtZppizZQ7hxj+nbjujN6cXLP5j2OX3VdQc1zJ3+4+CSFvUSFgl6anU1F+3jhszW8NCePouJSBqancO3pvTi3f7dmNY7vnCNv+17GPvkpO/bW3kMgNSWRT+8e5UNlEjQKemm29paU8fqC9Tw7azWrt+6he3IrrhmRwaVDm+Y4fnFJOYvyC1mQt4MFawtZuG4HW3eXHPb+Bqx+8PzGK1ACS0EvzV5FhWPG8i08MyuXz3O30yY+xCVDvXH89A7+jOM751i3vdgL9cr/lm3cdWCrx16d2jC4RwpDerTn8ekr2bIr/HUF15yWwY0jT6BzUkJjli8Bo6CXQPlyfRETZ63mrUUbqHCOMf28cfwhPRp2HL+4pJzF+YUsyPM69py8g916m/gQA9NTDgT74B7t6dAm/sBjw43RJ8TGMDg9hblrdxAfiuGaERlc/+3jSWkdX+u5ReqioJdA2lS0j398toaXK8fxB6WncN0ZvTinXzdij3Ecv2a3npNXyLKNOykL060P6dGezG5JhGKO/EvmcLNuVm/dw58+/JppizbQNj6W6844nmvP6EXbBF3mIpFT0Eug7S0p4/X5+UyctZo12/aSmpLIuBEZXDI0nXat4iKa1li7Wy9k625vqKV1fIiBaSkM6Rm+W4+W5Zt28uj7X/P+V5tp3zqOG876Fj8ZnkGruFDUn0uCR0EvLUJ51Tj+J7nMWb2dtgmxnNwjhc9Xb2d/WcWB+yXGxXDHmEw6tU1gwdodLKjRrWd0bO0Fes/2DOmRQmbXpGP+hFAfi9YV8sj7K/hk5Va6JCVw86gTuHRoD+Jjm89sI2l8CnppcZbkFzFxVi5TF2444v0S40IMTE8+MAQzuEcKHds2jZOic3K38cj7K5i7ZgepKYnc+p3eXDw4tVF/6UjzoaCXFqvX3W9zuH/h/7r5dLK6NW63Xl/OOWau3Moj761gyfoiju/chtu/cyLnn3QcMXWcE5CWRZuDS4vVPSX8ip2pKYn0T01u0iEPYGaceWJnpt00gqeuPJnYGOPmV3I4/y+z+PCrzTTFRk2anqb9r1zkGE0Yk0lijZOZiXEhJozJ9Kmio2NmnNO/G/++9dv86dJB7C0p47oX5nHx32bz6aqtfpcnTZyGbiTwgriYWGl5BZPn5/P49JVsLNrH8OM7cseYTE7u2d7v0sQnGqMXCah9peW8PCePv368iq27SxiV1YVfnH0i/VOT/S5NGpmCXiTg9paU8fzsNfzff3IpKi7lvJO68YuzT+SELkl+lyaNREEv0kIUFZcy8ZNcJs5aTXFpOWMHp3Lb6BMDta6/hKegF2lhtu3ez1P/+YYXPltLeYXj0qHp3DyqN92SWwXynIUo6EVarM079/GXGSv559x1xJgx/PgOfL56O/tKq18prA1QguCY59Gb2TlmtsLMVpnZ3WFuTzazt8xskZktNbNx1W67vfLYl2b2ipm1OvqXIiL10bVdK3439iRm/PIsLhzYnY+/3npIyAMUl5bz8HsrfKpQGkOdQW9mIeBJ4FygL3C5mfWtcbcbga+ccwOBs4A/mlm8maUCtwDZzrn+QAi4LIr1i0gE0ju05pEfDeRw19JuKCxu1HqkcUXS0Q8DVjnncp1zJcCrwPdq3McBSeYtBt4W2A6UVd4WCySaWSzQGjjy4iMi0mAOd6Vwt2R90A6ySII+FVhX7ev8ymPVPQH0wQvxJcCtzrkK59x64BEgD9gIFDnn3g/3JGb2czObZ2bzCgoK6vkyRCQS4a4UBm+Z5lkrdYVtUEUS9OE+7dU8gzsGWAh0BwYBT5hZOzNrj9f996q8rY2ZXRnuSZxzTzvnsp1z2Z07d46wfBGpj7GDU/nDxSeRmpKI4a35c+vo3nRoE8+VE+dw7xtL2L2/rM7vI81LJFvY5APp1b5Oo/bwyzjgQedN4VllZquBLKAnsNo5VwBgZlOA04AXj7VwETk6Ywen1pphc8NZ3+LRD77m75/k8p8VBTz0wwGMOKGTTxVKtEXS0c8FeptZLzOLxzuZOq3GffKA0QBm1hXIBHIrj59qZq0rx+9HA8uiVbyIREeruBD3nteHyeNPIyE2hh8/M4dfqbsPjDqD3jlXBtwEvIcX0pOcc0vNbLyZja+82wPAaWa2BJgO3OWc2+qcmwNMBhbgjd3HAE83wOsQkSg4uWd73rn1DH52Ri9e/iKPMY/NZLZWx2z2dMGUiIQ1f+12Jry2mNyte/jxKT2457w+2rC8CdPGIyJSbyf37MA7t57Bdad73f05f1J331wp6EXksFrFhfjvC/ry2vXDiQvFcMUzc/jvqUvYo7H7ZkVBLyJ1ys7owDu3eN39S3PyGPOnmcz+pvl391Nz1jPiwRn0uvttRjw4g6k56/0uqUEo6EUkIonxNbr7v8/h11O/bLbd/dSc9dwzZQnrC4txwPrCYu6ZsiSQYa+gF5F6qerurz29Fy/OWcs5f26e3f1D7y6nuLT8kGPeAm/Lfaqo4SjoRaTeEuND/PqCvky6fjghs2bT3W/bvZ/X5+dz40sL2FC0L+x91hfu4/5pS5n5dQH7y8rD3qe50fRKETkmxSXeMsfPzV5NWvtEHvrBQIZ/q6PfZQHgnGPZxl18tGIL05dtJmddIc5B56QE9uwvY29J7SBPiPX63/1lFbSOD3FG706MzurKWVmd6ZLUdBd/08YjItLg5q7ZzoTXFrFm215+Mrwnd52TRRsf5t3vKy1n9jdbmb5sCzOWb2FjZec+IC2ZUVldGJ3VlX7d2zFt0QbumbLkkOGbqk1YxvTrxme5tb/HwLRkRmV1ZXSfLvTr3g7vgv+mQUEvIo3Cr+5+Y1ExM5ZvYcayLXz6jbe5Suv4EKef0InRfbowMrMLXdrV7sYj2Vax6lPBjOWbmb58CwsrPxV0bZfAqKwujMrqyogTOtI63t+LyRT0ItKovli9nTsne9391cN7cte5WVENwooKx6L8QmYs38L0ZVv4auNOANLaJ/KdPl0ZldWFU47vQEJs7SWZj9XW3fv5eEUBM5ZvZubXW9m9v4z42BhO+1ZHRmd1YWRWF9LaN/5m7Ap6EWl0xSXlPPTecp6fvYb09q156IcDOPX4o+/ud+0rZdbKrUxfvoWPV2xh6+4SYgyye3ZgVJ8ujM7qwgld2jbqcEpJWQVz12yvHOLZzJptewHI6pbkDRP16cKg9PaEYhq+JgW9iPjmi9XbmTB5EWuPortfu23PgXHyOau3UVruaNcqlrMyvRA988TOpLSOb+BXELncgt0HPmXMXbOdsgpH+9ZxjMz0Ov1vn9iZ5MS4BnluBb2I+Cpcd7+paF+t8fHzBxzH/LU7KsNyM98U7AHghC5tGZ3VhVFZXTi5Z3tiQ01/ZnhRcSmfrCxgxrItfLRiCzv2lhKKMYZmtGd0VldG9enC8Z3aYGYRnSuoi4JeRJqEObnbuPP1xazdtpdQjFFecTB/QgZxIWNfmSMuZJx6fMfKk51d6NmxjY9VH7vyCsfCdTsOfDpZvmkXABkdW9OzQ2s+y91OSXnFgftXzf6pT9gr6EWkydhbUsbQ33/Inv2157C3jg/x6CWDOL13p0AviZy/Yy8fLd9Seb4h/B7ZqSmJfHr3qIi/p5YpFpEmo3V8LHvDhDx4Qzzn9O8W6JAHSGvfmquGZ/D8uGFhN+UG2FBYHLXnU9CLSKPrnpJYr+NB1hjvhYJeRBrdhDGZJMYdOsc9MS7EhDGZPlXkn8Z4L4L9+UhEmqSqk4zHOtMkCBrjvdDJWBGRANDJWBGRFkxBLyIScAp6EZGAU9CLiAScgl5EJOAU9CIiAaegFxEJOAW9iEjAKehFRAJOQS8iEnARBb2ZnWNmK8xslZndHeb2ZDN7y8wWmdlSMxtX7bYUM5tsZsvNbJmZDY/mCxARkSOrM+jNLAQ8CZwL9AUuN7O+Ne52I/CVc24gcBbwRzOr2sjxz8C7zrksYCCwLEq1i4hIBCLp6IcBq5xzuc65EuBV4Hs17uOAJPO2X28LbAfKzKwd8G1gIoBzrsQ5Vxit4kVEpG6RBH0qsK7a1/mVx6p7AugDbACWALc65yqA44EC4DkzyzGzZ8ws7OaPZvZzM5tnZvMKCsJvrSUiIvUXSdCH2+mq5trGY4CFQHdgEPBEZTcfCwwB/uacGwzsAWqN8QM45552zmU757I7d+4cWfUiIlKnSII+H0iv9nUaXude3ThgivOsAlYDWZWPzXfOzam832S84BcRkUYSSdDPBXqbWa/KE6yXAdNq3CcPGA1gZl2BTCDXObcJWGdmVXtijQa+ikrlIiISkTq3EnTOlZnZTcB7QAh41jm31MzGV97+FPAA8LyZLcEb6rnLObe18lvcDLxU+UsiF6/7FxGRRqKtBEVEAkBbCYqItGAKehGRgFPQi4gEnIJeRCTgFPQiIgGnoBcRCTgFvYhIwCnoRUQCTkEvIhJwCnoRkYBT0IuIBJyCXkQk4BT0IiIBp6AXEQk4Bb2ISMAp6EVEAk5BLyIScAp6EZGAU9CLiAScgl5EJOAU9CIiAaegFxEJOAW9iEjAKehFRAJOQS8iEnAKehGRgFPQi4gEnIJeRCTgFPQiIgGnoBcRCbiIgt7MzjGzFWa2yszuDnN7spm9ZWaLzGypmY2rcXvIzHLM7F/RKlxERCJTZ9CbWQh4EjgX6AtcbmZ9a9ztRuAr59xA4Czgj2YWX+32W4FlUalYRETqJZKOfhiwyjmX65wrAV4FvlfjPg5IMjMD2gLbgTIAM0sDzgeeiVrVIiISsUiCPhVYV+3r/Mpj1T0B9AE2AEuAW51zFZW3/Qm4E6jgCMzs52Y2z8zmFRQURFCWiIhEIpKgtzDHXI2vxwALge7AIOAJM2tnZhcAW5xz8+t6Eufc0865bOdcdufOnSMoS0REIhFJ0OcD6dW+TsPr3KsbB0xxnlXAaiALGAFcZGZr8IZ8RpnZi8dcdTiLJ8Fj/eH+FO/PxZMa5GlERJqbSIJ+LtDbzHpVnmC9DJhW4z55wGgAM+sKZAK5zrl7nHNpzrmMysfNcM5dGbXqqyyeBG/dAkXrAOf9+dYtCnsRESIIeudcGXAT8B7ezJlJzrmlZjbezMZX3u0B4DQzWwJMB+5yzm1tqKJrmf5bKC0+9FhpsXdcRKSFi43kTs65d4B3ahx7qtrfNwDfreN7fAx8XO8KI1GUf5jj6+CLv0P6MOjSD0IRvVwRkUAJRvIlp1UO29RgMfDOHd7f49pA6hBIG+oFf9pQaNOpcesUEfFBMIJ+9H3emHz14Zu4RLjwcS/U182F/LmQ/wXMfhwqyrz7tO91MPTV9YtIQAUj1QZc4v05/bfeME5ymhf+VcfbZ8CAH3l/L9kLGxfCui+88P/mI1j8T+82df0iEkDmXM0p8f7Lzs528+bNa5wncw4K1x7a9W9aoq5fRJoVM5vvnMsOd5sSy8zr+I+m668K/+pd/+JJh/9kISLiAwV9OPGtoedp3n8QvusPN9ZvMbB0CpTt945XzecHhb2I+EZBH4lIu/49W2o/tmo+v4JeRHyioD9a4br+/2lP7WWAOPw8fxGRRqAdpqLFzBuTD+dwx0VEGoGCPppG3+fN368uJtY7LiLiEwV9NA24xLtIKzkdMIhv652wjW/jd2Ui0oJpjD7aBlxy8MRr2X6YeDZMvQGu/wTa9/S3NhE5VAuZDq2OviHFJsCPnvdO1E4eB2UlflckIlVa0PLmCvqG1uF4uOgvsH4+fHi/39WISJUWtLy5hm4aQ7+xsPbn8PmTkDECss73uyKRlsc52LHGu+5l3RfhV7yFQE6HVtA3lu/+DtbN0Xi9SGMp2Qsbcrwr2ddVXtG+p8C7La4NhBKgfH/tx7Xr3rh1NgIFfWOpGq//vzO98fpx70JsvN9ViQRDzW49/wvY9CW4cu/2Dt+CE75TbXHCvvDl67WXNwcgBnZthqSujf0qGoyCvjFVjde/drU3Xn/O//pdkUjzVFe3njoETr8N0qqWG+9Y+3uEW9683/dh7jMw8Ttw5RTo1LvRXlJD0jLFfnhnAnzxNFz2ssbrReoSSbd+yFLifSEmdPTPt34BvHyJdw3MFZO879kMHGmZYgW9H6rm1+9Yo/H6lqSFzNmO2OHej0i69fRhR+7Wj9X2XHjxB7BzA/xgIvS5IPrPEWUK+qZoe643Xt+pt8brW4KqOdvhtrtsiWEf7v2IiYWk7rBrw8ElwKPdrdfHnq1eZ78hB857GIZe1zjPe5QU9E3V0qneeP2pN2q8PqgqKmDr1/DcOVC8o/btScfBL5c3fl1+qerWX70c9hXVvj2UAKfd1LDden2U7IHJP4Wv34Uzfgmjfu0tYNgEaYeppkrz64NnXxHkzzs4nrx+XvhAq7JrI/yxD6QP9cItfRgcN9CbpdXc1TW2Hk55SdNaBDC+DVz6Erz9C/jkj95QzkV/gVCc35XVi4Leb5pf33xVdetVu46tmwsFy/H2JDBvmKHf970An/Fb2LWp9vdolQI9h3uP/epN71go3gv7tGGQlu2Ff3NY6ro+M2Hevt0LzZqa4usMxcKFf/Zq++j3sHszXPICJCT5XVnEFPR+0/z65uNI3XqrFG+oof/F3p+pJ0OrdgcfG4oLP0Z/3sMHx+h3bTq4Y1n+XJg30fu0B97YdVPq+uszbz1tqPdLL1QtbvbvDP9+NKVuvjozOPNOb6jtrVvhufPgx5ObzVx7jdE3FRqvb1oi6darB2/HE+oeu63vrJuyEti85GB3vG4uFOV5tzV211+yFzYsqAz1edGZCdNcZyGt/AAm/QTadGpSc+11Mra50Pz6hhFJoBypW09sX9mZVoZqzW69MdXs+jfkQNk+77ZIu/663o/6zFsP1623BE1wrr2CvrnQ/ProO9y0xjPvhtYdo9et+6Wurr/bgEOnJ66dXfv9iE2E4Td6Jx6rfoE09rz15qiJzbVX0Dcnml8fXY/1P/wqhdC0uvVoOdD1Vwb/xoUHu34LHXnWi7r1+mlCc+0V9M2Nxuujo6Icftvh8LffNK9pd+vRUr3rf/euw9zJYMI36taPRhOZa3+koI9o4xEzO8fMVpjZKjO7O8ztyWb2lpktMrOlZjau8ni6mX1kZssqj996bC+lheg3FoZVzq9f/rbf1TQ/+3fDnKfhibD/5j3J6d6npqCHPHifClNPhlPHV+5nHEZymkL+aFXNtR9ytTfXfuoNUF7qd1WHqDPozSwEPAmcC/QFLjezvjXudiPwlXNuIHAW8EcziwfKgF865/oApwI3hnmshPPd33kn06beADvW+l1N81C0Hj74DTzWF/49AVp3gmHXe2Py1TXlaXwNbfR9ej8aQtVc+5G/gkWveMM5+3f5XdUBkXT0w4BVzrlc51wJ8CrwvRr3cUCSmRnQFtgOlDnnNjrnFgA453YBy4DUqFUfZNpvNnIbcuD16+DPA2D24/CtUXDth3DdB3DeQ956MsnpgHl/ttT1ZcB73Xo/GkbVXPuLnoDc/3hz7Xdt9rsqIIIxejP7IXCOc+66yq+vAk5xzt1U7T5JwDQgC0gCLnXOvV3j+2QAM4H+zrmdYZ7n58DPAXr06HHy2rXqYgGN1x9ORbk3JvrZk7D2U4hPgpOv9oa8NFtJ/ObDXPtjHaMPN4hZ87fDGGAh0B0YBDxhZgemLphZW+B14LZwIQ/gnHvaOZftnMvu3LlzBGW1EBqvP1T18fdXr4DCdTDmf+EXX8GY3yvkpWnofTZc87Y3jXXi2d4sKB9FEvT5QPUzOGlAzUUqxgFTnGcVsBqvu8fM4vBC/iXn3JRjL7kF0nh9+PH3H/0Dbsnx5oA39ymREjypQ+Da970pvP+4EJb9y7dSIgn6uUBvM+tVeYL1MrxhmurygNEAZtYVyARyK8fsJwLLnHOPRq/sFqYlj9cfafy931jN8ZamrcPxcO0H0LUfTLrK26bQB3UGvXOuDLgJeA/vZOok59xSMxtvZuMr7/YAcJqZLQGmA3c557YCI4CrgFFmtrDyv/Ma5JUEXdV+s+vne/vNBllFuTdM9dx58PRZsOJdOGU83LLQ+4WXPtTnAkXqoU0nuPot6P1dePuX3vITjXz9ki6Yam6CvB7O/t2w8GWY8zfvCuHkHt7c78FXaWhGmr/yMm9d+wX/gIGXR31de208EiRBXL++aL33y2v+c95CYmnDYPRvIOsCDc1IcPi4rn1EV8ZKExKk8XqNv0tL49Nce/0kNUdV4/WvXe2N1zfV+fXhlsPt/4Pa899PGa/579KyDLkKkrp5c+0nfgeG/sz7VNtAa/NrjL45a8rj9eGWB46Jg8QUbwlcjb+LeOva/+MiKKmxXEJcYr2vWD7mRc2kiWrK8+un//bQkAeoKPXG4DX/XcSTOgQS2tY+Xlrs/QxFiYK+OWtq4/W7NnsXhXxw3+HXgC8v1fi7SHXhNo0HbxgnSvTT1tz5NV5fXgqblhy63Vxh5c5GMXHe7kblYX7xNOS+piLNUXJa+MYoij8rCvog6DcW1lauh5MxomHG63dtrtyxqHJz6A05UFY5NFO1V+mw673diboNgGXTwm/hp+VwRQ41+r4G/1lR0AdFNOfX19WtHzcQsscd3Ic0XOdRdRKprk25RVq6RvhZ0aybIDna/WYP6dbnVnbrlXuMVnXrVRtldxsAca0a7jWIyFHRlbEtRfXx+od6eXtZ1uwOykth02Jv+OWw3fpPj9yti0izoqAPmvISiImFkt3e10Xr4M0bYclk2L8zfLdefWxd3bpI4Cjog2b6b6Gi7NBj5SWw8j1IzVa3LtICKeiD5rBzbw1+Nr1RSxGRpkEXTAXN4bp0de8iLZaCPmhG3+fNwa1O89dFWjQFfdAMuMRbDCk5HTDvz3oujiQiwaIx+iAacImCXUQOUEcvIhJwCnoRkYBT0IuIBJyCXkQk4BT0IiIB1yRXrzSzAuBo98brBGyNYjnNmd6LQ+n9OJTej4OC8F70dM51DndDkwz6Y2Fm8w63VGdLo/fiUHo/DqX346CgvxcauhERCTgFvYhIwAUx6J/2u4AmRO/FofR+HErvx0GBfi8CN0YvIiKHCmJHLyIi1SjoRUQCLjBBb2bnmNkKM1tlZnf7XY+fzCzdzD4ys2VmttTMbvW7Jr+ZWcjMcszsX37X4jczSzGzyWa2vPLfyHC/a/KTmd1e+XPypZm9YmaB2zg5EEFvZiHgSeBcoC9wuZn19bcqX5UBv3TO9QFOBW5s4e8HwK3AMr+LaCL+DLzrnMsCBtKC3xczSwVuAbKdc/2BEHCZv1VFXyCCHhgGrHLO5TrnSoBXge/5XJNvnHMbnXMLKv++C+8HOdXfqvxjZmnA+cAzftfiNzNrB3wbmAjgnCtxzhX6WpT/YoFEM4sFWgMbfK4n6oIS9KnAumpf59OCg606M8sABgNzfC7FT38C7gQqfK6jKTgeKACeqxzKesbM2vhdlF+cc+uBR4A8YCNQ5Jx739+qoi8oQW9hjrX4eaNm1hZ4HbjNObfT73r8YGYXAFucc/P9rqWJiAWGAH9zzg0G9gAt9pyWmbXH+/TfC+gOtDGzK/2tKvqCEvT5QHq1r9MI4Mev+jCzOLyQf8k5N8Xvenw0ArjIzNbgDemNMrMX/S3JV/lAvnOu6hPeZLzgb6m+A6x2zhU450qBKcBpPtcUdUEJ+rlAbzPrZWbxeCdTpvlck2/MzPDGYJc55x71ux4/Oefucc6lOecy8P5dzHDOBa5ji5RzbhOwzswyKw+NBr7ysSS/5QGnmlnryp+b0QTw5HQgNgd3zpWZ2U3Ae3hnzZ91zi31uSw/jQCuApaY2cLKY/c6597xryRpQm4GXqpsinKBcT7X4xvn3BwzmwwswJutlkMAl0PQEggiIgEXlKEbERE5DAW9iEjAKehFRAJOQS8iEnAKehGRgFPQi4gEnIJeRCTg/j8McNdWId5ZPgAAAABJRU5ErkJggg==\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# plot of train and test scores vs tree depth\n", "plt.plot(values, train_scores, '-o', label='Train')\n", "plt.plot(values, test_scores, '-o', label='Test')\n", "plt.legend()\n", "\n", "# Insert code to display your plot here ...\n", "plt.show()\n"]}, {"cell_type": "markdown", "id": "284e067a", "metadata": {"id": "284e067a"}, "source": ["We get an accuracy of between 0.87 to 0.91 (or between 87% - 91%) of the predictions made in the training set are correct. Let's think about it. Is this accurancy acceptable for you?"]}, {"cell_type": "markdown", "id": "2051d116", "metadata": {"id": "2051d116"}, "source": ["Your response here ...\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "e48a76cd", "metadata": {"id": "e48a76cd", "outputId": "bc90c326-6871-4aed-d4d3-4466a92bbf86"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Training Accuracy: 0.9120310478654593\n", "Test Accuracy: 0.8372093023255814\n"]}], "source": ["# From the plot above, build a new KNN model from the K value that produced the best test accuracy result ...\n", "\n", "train_scores, test_scores = list(), list()\n", "\n", "knn = KNeighborsClassifier(n_neighbors=??)\n", "knn.fit(??, ??)\n", "\n", "# Predicting on the train dataset\n", "y_pred_train = knn.predict(??)\n", "train_acc = accuracy_score(??, ??)\n", "\n", "# Predicting on the test dataset\n", "y_pred_test = knn.predict(??)\n", "test_acc = accuracy_score(??, ??)\n", "\n", "print(\"Training Accuracy: \" + str(train_acc))\n", "print(\"Test Accuracy: \" + str(test_acc))\n"]}, {"cell_type": "markdown", "id": "22a7bf8c", "metadata": {"id": "22a7bf8c"}, "source": ["## Model Evaluation\n", "\n", "### Confusion Matrix\n", "\n", "A confusion matrix is a table with 4 different cases that represent the 4 possible outcomes in a binary classification problem. The model makes a correct prediction have 2 possible cases:\n", "\n", "- True Positive (TP): The model predicts the positive class and the observation actually belongs to the positive class. In this problem, the model predicts default and the customer actually defaults in his/her credit card payment.\n", "\n", "- True Negative (TN): The model predicts the negative class and the observation actually belongs to the negative class. In this problem, the model predicts customer will pay his/her credit card bill (non default) and he/she actually makes the next payment.\n", "\n", "On the other hand, when the model makes a mistake, again 2 possible cases can be happening:\n", "\n", "- False Positive (FP): The model predicts the positive class and the observation actually belongs to the negative class. In the problem, the model predicts default and the customer actually makes his/her payment.\n", "\n", "- False Negative (FN): The model predicts the negative class and the observation actually belongs to the positive class. In the problem, the model predicts the customer will pay his/her bill (no default) but the customer actually default it.\n", "\n", "![ConfusionMatrix.png](attachment:ConfusionMatrix.png)\n", "\n", "Let's compute the confusion matrix for this KNN model for the laudromat use case.\n", "    "]}, {"cell_type": "code", "execution_count": null, "id": "def044c5", "metadata": {"id": "def044c5", "outputId": "fa338142-d381-4c15-98ef-09799c792f87"}, "outputs": [{"data": {"image/png": "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**********************************************++5Km0DDvFvn2y0/5bwGWqK0BNPnVeBGyUNNrPRAJL2Ab7H+brHAk2Ag4BhQEugQdL9K8rsTwJOB673aZCWmtmPFTW0pN2AUjP71B/qCnyZdMkpOD/8KcCUzf6Um0FBQQGXXzmM8wYPpbS0lP7H96N9h3Y5rf+va0fx8fufsPKHlfz+xGEcd9axHNQ3M1/qOLZPNvWzUUam9a+49BqmvTud5cuX0+eQY/n1+efS/8ToXXPlkatx4irPh1XTSNoR52/eG1gDfIHzLw8B+uB6wDeY2aPejz0eaArcj8sQPQGoBdwEvAzcB7QFVgFDzGyWpBHASjO7zZc5BxeVsj1wJ7AdUAx85u9ZKukLr3U07lfMr8zss2QtSROBS81smqSmwDSfvaNCUnWn5CohKUQgXbKRFKJ+QZO0rPD+o99K+Xs6dXCPrFn8nDTiuYo34t3MbGmUusGIV04w4ls+cTDiB4yZmvL3dPI5+2fNiOeiOyUQCARyjlwd2AxGvBpU5RYJBAJbLrnqEw9GPBAIBFIgJIUIBAKBGJOjNjwY8UAgEEiFvPxcnFYTjHggAjIdPXLnB+9nVP93nfbMqH6gagqUX9NVqJLQEw8EAoEYE3zigUAgEGOCEQ8EAoEYk6MRhsGIBwKBQCqEOPFAIBCIMXnBiAcCgUB8CT7xQCAQiDHBnRIIBAIxJkc74jmZ2SdQCZPfmMKxRw+g75EnMGb02K1ef+XS73h++F94/MLhPHHRCOY8/woA0x55mv9cch1PXno9L173N376bnnaZUH82qcmyoi7fkXkamafjBpxSSU+6/xMSdMl9ajm/RuyyJc53sYncUBSN0l/38z6dZE0I2n/V5JW+UQTSPqlpFnV1NxQt6gpKSnhxhtu4e6Rd/DUs48y/oUJzPts/latn5efz36DTuKkO67l2Jsu44PxE/l+wTfscdwRnPjXazjhtqtpvfcevP/48zlZ/2zqZ6OMuOtXxlZpxIHVPut8F+ByXKadSDGzaWY2dDNvnw3sLCmRzq0H8BGwZ9L+5DSrGBlzZs+l9U6taNW6JbVq1+KoPkcw8dVJW7V+3caNaLrLTgDUrrMt27XcgZ++W07tunU2XFO8dm0kKdfj2D7ZLiPu+pWRn2cpb9kkm+6Uhrg8mQBIGibpXUmzJF2bdPxKSR9L+h+wW9LxvX2PfgpwftLxXpKe8+9HSLpX0kRJ8yUNTbruakkfSXpZ0iOSLjWzUuBdYD9/2d7AXTjjjX99S9K+kt6S9L5/3c1r7i7pHf9rY5akDv6+fEmjJc2V9JKkjRYlDYoWL6FFi+Yb9gtbFLK4aEkU0luE/oqipSz74isKO7QF4N2H/8sjv76MeW+8w96npJ+HMe7tk40y4q5fGVLqW9Va2tbbjpneTlzrjzfxNupT/9q4Kq2UjLikE7zoD5J+lLRC0o8p3FrHG7iPgHuA673eEUAHYF9cIuK9JR0kaW/gVFxP+ARgnySt+4ChZta9ijI7Akd67eGSaknqhstYn9DtlnT9W0APSfWAUmAimxrxybje+UFmtidwDXCjP/8b4A4z6+o1F/rjHYC7zGx3YLkvexMkDZE0TdK0MaPvr+IjOcpLpRflD7c4669fvYb/3TaS/c88eUMvfJ/T+vOrkTfT7sB9+WD8a2mXEef2yVYZcdevjDxZylsKrAUO8V6KrsBRkvYHLgNeMbMOwCt+v1JSjU65BehnZh+meH2C1d7AIak78ICkzsARfkssT1cfZ/gaAE+Z2Sp/zzP+tRGwnZm97q8fh0uYXB7Pm9laYK2kIqA50BN42sxWe71nk66fDPweeAN418zmSWovqRlQ38zmS2oNjPU9bcMlYQaX7f5KSa2AJ83sU+8P+9zMZvhr3gPalK2kmY0CRkHqOTabtyhk0aLFG/aLFhVRWNgslVtTIq76pcUl/O+2kbQ/cF/a7r/Xz863O3BfXrrxH2n3xuPaPtksI+76lRHxw8iAlX63lt8MOA7o5Y+PxXUq/1iZVqrulMWbYcA3wcym4DLSN8O1x03eX97VzNqb2ZjEpeXcrgqOl8fapPcluAdVZe0/Fdfj74kzyuB61KfieungfkG8ZmadgX7Atv4zPQwcC6wGJkg6pJI6pM3unTvx1ZcLWLjwa9avW8/4F1/i4N4HRiEdW30zY9LdD7Bdqxb8st/hG47/8O3GL/pX786kUcsWaZUD8WyfbJcRd/3KqE5PPPmXtt+GlNWTlO8DK4qAl83sbaC5mX0L4F8Lq6pXqsZlmqRHgf+SZKDM7MkU70dSRyAfWAZMAK6X9JCZrZTUElgPTALul3Szr1s/YKSZLfeunJ5m9iZweqrlet4ERkq6yeseA4z2n2GFpAXAmWx8Ak4BLgLu9vuNgK/9+zOTPtMuwHwz+7t/vweQsaHygoICLr9yGOcNHkppaSn9j+9H+w7ttmr9xR/N47NJU2m8U0uevPR6wLlRPn5lMj98sxgk6jdrQs8h1f0vk536Z1M/G2XEXb8y8qoxgpj8S7uSa0qArpK2A57yXopqo/J8TD+7SLqv/DrY2VXcV4KLAAHXG77CzJ735y4EzvXnVgIDvSvjSuAM4Etcj/gDM7vN+8vvBVbhHgIDzKyzpF7ApWbWV9IIYKWZ3ebLmAP0NbMv/Llfed0lwEQzG+2vuws4zsxa+f1ewGtADzOb4l1BY/19rwL/Z2ZtJF0ODMQ9gBYBp+EGcJ/zvXbkQiTrm9mIitopVXfK1kpIChGIgm3zG6XlETn95Rkpf08fOrxrtcqSNBz4CRgM9DKzbyXtgLNTu1V6bypGfEtAUn3f66+L6/EPMbPpNV0vCEa8KoIRD0RBukZ8YDWM+INVGHE/5rbeexnqAC8BfwYOBpaZ2c2SLgOamNkfKtNKyZ3iB+7uBA7A+abfBC40s4WV3phbjJLUCefPHpsrBjwQCMSDFKNOUmUHXLBEPm5s8jEze86HUD8m6RzgK+CkqoRS9YnfBzycJDjQHzu8wjtyDDM7rabrEAgE4kuUEzHNbBYbJxUmH18GHFodrVRd9c3M7D4zK/bb/bgok0AgENgqkCzlLZukasSXShroQ2LyJQ3ERZkEAoHAVkG+LOUtm6RqxM8GTsZFYHwLDPDHAoFAYKsgT6lv2SQln7iZfYWb1BIIBAJbJdl2k6RKpUZc0h/M7BZJd1LOjMk0Vg8MBFLmvF/skVH93W96J6P6cy/fN6P6geyQo4l9quyJJ6baT8t0RQKBQCCXUcorf2SXSo24mSUWilplZo8nn5NUZfxiIBAIbCnEPT3b5SkeCwQCgS2SXE0KUZVPvA9wNNBSm6ZAawgUZ7JigUAgkEtEPGMzMqryiX+D84cfi1sXO8EK4OJMVSoQCARyjRz1plTpE58JzJT0sJmtz1KdAoFAIOfIVZ94qmuntPFrcScWkALAzHbJSK0CgUAgx8hVd0qqA5v3Af/E+cF7Aw/gUqQFsszkN6Zw7NED6HvkCYwZPTbol+Haq27gsAOP5uTj0k8CAVA7P48nz9qX5wbvz4u/7s6FB7l+S8fC+jx+5j68MGR/Rp3clfq18yMpL9Ptk40y4q5fEXGfdl/HzF7BrT/+pU9wcEhlN0gq8UmSZ0qaLqlHZdeXc/8In1Ch7PE2PtkDkrqVGXCtFpLul/R5Uj0PTTp3j1+6NmcoKSnhxhtu4e6Rd/DUs48y/oUJzPssukRCcdcH6Nf/GO4ceXtkeutKShn44Hv0HT2VfqOnclC7pnRt2Yib+nbi1lc/4+hRU3np4yIGd2+TdlnZaJ+4/42z0UYVEWW2+yhJ1YivkZQHfCrpAknHU3Xut9U+f2YXXDjiTelUtDzMbFoEs0aH+WTOFwH/StI+18w+SFM7UubMnkvrnVrRqnVLatWuxVF9jmDiq5OCfhJ7dduTRo0aRqq5an0JAAV5oiBPmBltt6/HO199D8Dkz5dxZMcqUyFWSTbaJ+5/42y0UUXEfRXDi4C6wFBgb9x64oOqUU5D4PvEjqRhkt6VNEvStUnHr5T0saT/AbslHd/b95SnAOcnHe8l6Tn/foSkeyVNlDRf0tCk666W9JGklyU9Ul4PH5dXs2XSPRMldfPvV0r6k6/DVEnN/fGTJM3xxyf5Y/mSbk36fL+uRjtVStHiJbRo0XzDfmGLQhYXLYlKPvb6mSJP8Oy5+/POJQcz+fNlzPzmRz4tWslhu7rVmPv8ojk7NNy2CpWqyUb7xP1vXJP/h3J1AayUjLiZvWtmK81soZmdZWYnmtnUKm6r490UHwH34DLGI+kIoAOwL9AV2FvSQT6H5qm4hdJPwGWgT3AfMNTMuldRZkfgSK89XFItb4hPTNLtVsG9R+ESQZdHPWCq/1UxCZcHD+Aa4Eh/PLFA2DnAD2a2j/8MgyW1LSuopGzYY0bfX8XHcpSXSi/K/y9x188UpQb97pnKAXe8QZcdG7Frs3r88bm5DOzWmqfP2Y96tQtYX1KadjnZaJ+4/41r8v9QrvbEU03P9jJwkpkt9/uNgX+b2ZGV3LbauynwiYYf8Nmcj/BbInFifZxRbwA8ZWar/D3P+NdGwHZm9rq/fhzQp4IynzeztcBaSUVAc6An8LSZrfZ6z5a551ZJt+DcQ/tXoLsOeM6/f4+NGY0mA/dLegx40h87AthD0gC/38h/vs+TBZOzYaeaY7N5i0IWLVq8Yb9oURGFhdHl5oi7fqZZsbaYqV9+z0HtmnLP1C8582GX4a9Nk7r0bt80bf1stE/c/8Y1+X+oGsnus0qq9WqaMOAAZvY9VfvEN2BmU4CmuGxAAm7y/vKuZtbezMYkLi3ndlVwvDzWJr0vwT2kqnpQDwPaA1fhMtqXx3rb2AVI6GJmv/H3tQZmSNrel/e7pM/X1sxeSrH+lbJ750589eUCFi78mvXr1jP+xZc4uPeBUUhvEfqZoEndWjTYxvV1tinI44C2TZi39Ce2r1sLcH/sC3q25eHp6aebzUb7xP1vXJP/h3I1OiXVOPFSSTv5dcWRtDOpG1YkdQTycdmAJgDXS3rIZ59vCazHuSnul3Szr1c/YKTPBv2DpJ5m9iZQ3dixN4GRPs69ADgGGJ18gZmVSroDGCTpSDObkOLnamdmbwNvS+qHM+YTgPMkvWpm6yXtCnxtZj9Vs94/o6CggMuvHMZ5g4dSWlpK/+P70b5Du3Rltxh9gCsuvYZp705n+fLl9DnkWH59/rn0P3Hzl8JvVn8bbj12d/Il8iSe/3Axr322lDP3ac3Abq0BmPBREU/M/CbtumejfeL+N85GG1VErq4nrvJ8TD+7SDoK99M/4dI4CBhSmbGTVALMTuwCV5jZ8/7chcC5/txKYKCZzZN0JXAG8CWwEPjAzG7z/vJ7gVU4IznAzDpL6gVcamZ9JY0AVprZbb6MOUBfM/vCn/uV110CTDSz0ZLuB54zsyf8PScCvzWzQyVN9NrTJK00s/r+mgFe90xJT+JcJQJewQ0AC7gB9xCSL6+/mf1QUVul6k7ZWim2kozqd7n5vaovSoOwnnhusG1+o7Tc59e9/0bK39Nr9jwwa8M9KRlxAElNcT5jAVPMbGkmKxYlkur7Xn9dXI9/iJlNr+l6JQhGvHKCEQ9EQbpG/IYZk1L+nl7V9aCsGfGqVjHsaGYfSdrLH0r8ZtzJu1dyxhBWwSg/cWdbYGyM6h0IBHKEXI2kqson/ntcON1fyjlnVDFrM1cws9Nqug6BQCDeZHud8FSpahXDwf61d3aqEwgEArlJLHvikk6o7LyZPVnZ+UAgENhSyNVVDKtyp/Tzr4VAD+BVv98bmMjGCS6BQCCwRRPLnriZnQXg1yfpZGbf+v0dgLsyX71AIBDIDeLaE0/QJmHAPYuBXTNQn0AMyXQIYIGiWau7IjIdAjhs6scZ1Qe4df/dqr4okBax7IknMVHSBOARXFTKqcBrGatVIBAI5BjZnk6fKikZcTNLrCF+kD80ysyeyly1AoFAILfI1Wn3qfbEAaYDK8zsf5LqSmpgZisyVbFAIBDIJWK9iqGkwcATwEh/qCUVr70dCAQCWxy5up54qg+X84EDgB8BzOxTqrEUbSAQCMSdvGps2SRVd8paM1snnwFUUgHVWIo2EAgE4k6u+sRTfWi8LukKXMq1w4HHgbIZcgKBQGCLJcqkEJJaS3pN0oeS5vrluZHUxOcC/tS/Nq5KK9We+B9x63/PBn4NvIDLmxnIMpPfmMKfb/oLpSWlHD/gOM4ZXJ181TWrf+1VN/DG62/RpEljHnv6och0k4lb+6z5bhmz7xnJuh9+AIlWB/dm58OPZOY//8GqRW5qxvpVq6hVty7dr/1TFB8hdm2Ubf2KULSB4sXA781suqQGwHs+DeaZwCtmdrOky4DLcPa3Qqo04pLygFlm1pkyGXE2h6RkEcKlOrvAzN6qxv0jSEr+kHS8DS7BQ2efHPkMMxtajkQqZewP3AFs47dHzWxEJdfvCPzdzAZUdE0UlJSUcOMNtzDynn/QvHkhp50yiF69D6Rd+11iod+v/zGcfNpJDL/8ukj0yhLH9lFePrudchoNd25D8erVTL3uGrbv1Jku512w4ZqP//0wBXXrRPERYtlG2dSvjLwIPch+8uS3/v0KSR/iAkaOA3r5y8biljep1IhX6U4xs1JgpqSdNr/Km7Da557sAlwO3BSR7gbMbNrmGnDPWFziiK5AZ+CxKsr7JtMGHGDO7Lm03qkVrVq3pFbtWhzV5wgmvjopNvp7dduTRo0aRqZXlji2zzbbbUfDndsAUFCnDvV22JG1y7/bcN7MWPTu27TYr3ta5SSIYxtlU78ypOpsGiJpWtI2pGJdtQH2BN4Gmidmx/vXKgNIUvWJ7wDMlfSKpGcSW4r3VkZD4PvEjqRhkt6VNEvStUnHr5T0saT/AbslHd9b0kxJU3ARNInjvfx6L0gaIeleSRMlzZc0NOm6qyV95H1Pj0i61J8qZONTssTMPkjSGifpVe+zGuyPt/Hp4JB0pqQnJY3319wSQTsBULR4CS1aNN+wX9iikMVFS6KSz7h+pol7+6xeuoQVX31Jo13abzj2/Scfs03DRtRr3iKSMuLeRjX5f1TV2MxslJl1S9pGlasp1Qf+A1xkZj9uTr1S9YlfW/UlKVNH0gxclp0d8IklJB2By1e5L64dnpF0EPATbpr/nr6+04FEPq37cJnlX5d0ayVldsStvNgA+FjSP4EuwIkV6N7ur5sIjMdlA1rjz+2BS1NXD3hf0vPllNfV6671Onea2YLkC/yTeQjAP/75N84ZfGYl1XeUl0ovSjddpvUzTZzbp3jNGmbc9Xd2+9XpFNTZ6DpZ9PYUWuy3f0SlxLuNsqFfGVFPu5dUC2fAH0pa1nuxpB3M7Fu/0GBRVTpVrSe+LfAboD3Ojz3GzIrTq7pzp3j97sADkjoDR/jtfX9dfZxRbwA8ZWar/D3P+NdGwHZmlkjePA7oU0GZz5vZWmCtpCKgOdATeNrMVnu9DdE2ZnadpId8fU7DJVnu5U8n7lkt6TXcQ2dGmfJeSSRGlvQBsDOwiRH3T+ZRkHqOzeYtClm0aPGG/aJFRRQWNkvl1pTItH6miWv7lBYXM/Ouv7PD/j1ovvc+G4+XlFA0fRr7X3N92mUkiGsbZUu/MqJcxVAuXnsM8KGZ/TXp1DPAIOBm//p0lfWq4vxYoBvOgPeh/DRtm42ZTQGaAs1wD9SbvL+8q5m1N7MxiUvLuV0VHC+PtUnvS3APr0of4GY2z8z+CRwKdJG0fQV1Ka8O5ZWXNrt37sRXXy5g4cKvWb9uPeNffImDex8YhXRW9DNNHNvHzJh73z3U22FH2hy5aR/kuw/mUq/FDmzbpElaZSQTxzbKpn5lVMedkgIHAP8HHCJpht+OxhnvwyV9Chzu9yulKuPSycx+CSBpDPBOavVLDUkdgXxgGTABuF7SQz4zfUtgPS47/f2Sbvb17QeMNLPlkn6Q1NPM3gROr2bxbwIjJd3kdY/BR99IOgZ4wdxvtw44Q7zc33ecv6cernd+GVB7sxqgmhQUFHD5lcM4b/BQSktL6X98P9p3aBcb/SsuvYZp705n+fLl9DnkWH59/rn0P/HYyPTj2D7LP/2Eb6dMpn6r1kwZfiUA7U88iWZ7dGXRO1MiG9BMEMc2yqZ+ZUQ52cfbrIrs/aHV0VJ5PqYNJ6XpZrZXRfubQ1KIIbgPcYWZPe/PXYiLRwdYCQw0s3mSrgTOAL4EFgIfmNltkvYG7gVW4R4CA3yIYS/gUjPrWzYk0Q9A9jWzL/y5X3ndJcBEMxst6d/AXl63GLjSzCb463cE2gE7Abf469uwMbzxTKCbmV3gy3sOuM3MJlbUJqm6U3KVuK8nnmnCeuK5wbb5jdJynz82/4WUv6cn73J01oaTqjLiJbiBRXAGtw7OsPkBWMtcvFgWkFTf9/rr4nr8Q8xseiXXj6CcGPV0CUa8coIRr5pgxKsmXSP++Ocvpvw9Paltn6wZ8arSs8X721M1oyR1wkXKjK3MgAcCga2bvIinbEZFJANuccXMTqvm9SMyVJVAIJDj5KYJ38qNeCAQCKSKctSMByMeCAQCKZCj3pRgxAOBQCAV8kJPPBAIBOJL6IkHKmRK0ftVX5QG3Qv3zKh+3EMAM002wv8mfTsto/oH7dAto/pxIESnBAKBQIwJA5uBQCAQY3K0Ix6MeCAQCKRC6IkHAoFAjEk1g062CUY8EAgEUiAMbAYCgUCMUTDigUAgEF9y04QHI57zPPDnscyeMpsG2zXgmvuHA/Dsfc/y5vNv0qBRfQCOG9yfX+7/y0jKm/zGFP58018oLSnl+AHHcc7gQZHoBv2a03/o1nHMmer+D10x5uoNx19/6jUm/fd18vLz2X2/3en/6xPSLgvi2UapkKs98Vz11QNuPXOftmiuz2p/iaRK6yxpR0lP+Pcbst5XcO12kpb5fHdI6i7JJLXy+40kfVdVmeXorqzO9ZXR/aju/O6WoT87fuiAQ7lqzNVcNebqyAx4SUkJN95wC3ePvIOnnn2U8S9MYN5n8yPRDvo1p7/fkfvz25su2OTYJ+9/zKy3ZnHZ6Cu58t6rOfTkw9MuB+LbRqkQcXq2yMhpI45Pqmxmu+PyzR0NDK/sBjP7xswGpCJuZsuBRcAv/KEeuETNPfz+/sDbZla6GXWPhA5ddqVug7pZKWvO7Lm03qkVrVq3pFbtWhzV5wgmvjop6Mdcv/0eHajbsN4mx9589g0OP/VIatWuBUCDxg3SLgfi20apoGr8yya5bsQ3YGZFwBDgAjnaSHpD0nS/9QDwx+ck3yspT9Knkpol7X8mqSkwmY1Guwdwe5n9tyopawdJk/yvhTmSDkwq80/+18NUSc2jbo+JT03k+rOv44E/j+WnFT9VfUMKFC1eQosWG6ta2KKQxUVLItEO+jWvv0lZC4uYN/szbjv/Fu64+K98+dEX0ehuQW1Ulnwp5S2bxMaIA5jZfFydC4Ei4HCf8/MU4O+V3FcKPMjGZMqHATPNbCnwFhuN9i7A40BioYgeOCNfUVmnARPMrCvQBZjhj9cDpppZF1zat8Gb/aHL4eDjDuaGh2/gynuuouH2jfjP3U9Eolteqr4o/zsG/ZrVT6a0pITVK1fx+38M47hfn8C9148pt/zqsiW10c/KUepbNomVEfckmqgWMFrSbJzh7VTFfffiki0DnA3c599PBnpIagt8YWZrAEmqD+wNvFNJWe8CZ/ncm780sxX++Dog4Yt/D2jzsw8hDZE0TdK05x58NqUPnqBhk4bk5eeRl5dHz2N68sWHX1Tr/opo3qKQRYsWb9gvWlREYWGzSLSDfs3rJ7Nds8Z06dkVSbTp2IY8iZU/pD+UsyW1UVmCOyUCJO0ClOB6xhcDi3E94G5A7cruNbMFwGJJhwD7AS/6458CjYF+wBR/+XvAWcDnZrayorLMbBJwEPA1ME5S4iGx3jZ2GUooJwrIzEaZWTcz69Z3YL9qtcMPy37Y8H7GmzPYse2O1bq/Inbv3ImvvlzAwoVfs37desa/+BIH9z6w6huDfiz0k9njgD345H2XwLlowWKKi4up76Od0mFLaqOy5GpPPDYhht6f/S/gH2ZmkhoBC82sVNIgIJX1UO/BuVXGmW2Son0KcCFwZtL+DcALfr/csiTtDHxtZqMl1QP2Ah5I53P+rMLX3cMnMz5m5Q8ruWzAH+l3Vj8+mfEJCz5bgCS2b7E9p/9+YCRlFRQUcPmVwzhv8FBKS0vpf3w/2ndoF4l20K85/ftuuJfPZn7Cyh9WcvUpV3D0oGPY/6gePHTrOG4853ryCwoY+MdBkYTQxbWNUiFX105RFH6wTCGpBJiNc2cUA+OAv3pj2gH4D7AKeA34nZnVl9QGeM7MOkvqBVxqZn29Xi1gGbCvmX2UVM4w4E9AIzNb7TU+B04zs0cqKWsQMAxYD6wEzjCzzyWtNLP6XnsA0NfMzqzoc7727cSM/hEyvZ54oOYJ64lXzbb5jdKywm8veTPl7+l+zXpmzeLntBGPGkndgNvNLDu/v1IkGPFAugQjXjXpGvFpSyan/D3t1uyArBnx2LhT0kXSZcB5bIxQCQQCgZQJMzZrGDO72cx2NrM3a7ougUAgjuTmnM2tpiceCAQC6ZCb/fBgxAOBQCAlctWdEox4IBAIpECuhhgGIx4IBAIpEIx4oEL2aLJbTVchEHMyHQL48tdvZ1T/8Jb7ZVQ/EoI7JRAIBOJLbprwrSjEMBAIBNIjuhBDSfdKKkpeNltSE0kv+2WzX5bUOJVaBSMeCAQCKRDxKob3A0eVOXYZ8IqZdQBe8ftVEox4IBAIpICklLeq8Cugflfm8HHAWP9+LNA/lXoFIx4IBAIpUJ2eeHK+AL8NSaGI5mb2LYB/LUylXmFgMxAIBFKgOgObZjYKGJWpuiQTeuKBQCCQCpnPCrFY0g6uKO2AS35TJaEnHiPWrl3Lb8+6kPXr1lNSUkLvww7m3PPPirSMyW9M4c83/YXSklKOH3Ac5wweFPS3IP1MlPHobQ/xwdtzqL9dA4aNvgKAcTfcy5IFzgat/mk1derV4ZKRKY3TVUk22qg8sjDZ5xlgEHCzf306lZtyticuqcRnkZ/rs8ZfIqnS+kraUdIT/n0vSc9Vcf0ISV/7cj6Q9Kukc9dJOiyaTxMNtWvX5s57/soDT4xh7GP3MHXyO8yZOTcy/ZKSEm684RbuHnkHTz37KONfmMC8z+YH/S1EP1NldDtiPwbf+NtNjv3fVWdzycjLuGTkZfyyZxc69+ySVhkJstFGFRFldIqkR3AZxHaTtFDSOTjjfbikT4HD/X6V5KwRB1abWVcz2x33gY4Ghld2g5l9Y2YDqlnO7T5b/XHASJ/9BzO7xsz+txn1zhiSqFu3LgDFxcUUFxdHuijPnNlzab1TK1q1bkmt2rU4qs8RTHx1UtDfQvQzVUa7PdpTt0Hdcs+ZGTMnvc+evfdOq4wE2Wijiog4OuVXZraDmdUys1ZmNsbMlpnZoWbWwb+WjV4pl1w24hswsyJgCHCBHG0kvSFput96APjjc5LvlZTng+ebJe1/JqlpmTI+xaVfa+yvu9+nVkPSF5Ku9WXNltTRHz/Y9+JnSHpfUgN/fJikdyXNknRtlG1RUlLCoJPO4Zhe/dmnezd236NTZNpFi5fQokXzDfuFLQpZXLQk6G8h+tkqI5n5s+fRYLsGNGuVUqBFlWS7/puSm+uJx8KIA5jZfFx9C3EO/8PNbC/gFODvldxXikuOnMjocxgw08yWJl8naS/gU//AKI+lvrx/Apf6Y5cC5/ue/IHAaklHAB2AfYGuwN6SDqrep62Y/Px8xj4+hv++/DgfzvmQeZ9G91OyvFR9Uf53DPo1q5+tMpKZ8dp7dI2oFw7Zr3/ZcnLPhMfIiHsS7VMLGC1pNvA4UFV39F7gDP/+bOC+pHMXS/oYeBsYUYnGk/71PaCNfz8Z+KukocB2ZlYMHOG394HpQEecUd/0gyTFkY6958Eqqv9zGjRswJ7duvL25HeqfW9FNG9RyKJFizfsFy0qorCwWdDfQvSzVUaCkpISZr85k6699opMM5v1L0vEMzYjIzZGXNIuQAmuF34xsBjoAnQDald2r5ktwIXvHALsB7yYdPp2M9sN16N/QNK2Fcis9a8l+KgeM7sZOBeoA0z1bhYBN3l/flcza29mY8qp0ygz62Zm3QadOzCFFoDvv1vOih9XuMqsWcu0qe+xc9udUro3FXbv3ImvvlzAwoVfs37desa/+BIH944up3TQr1n9bJWR4NPpH1PYujnbNUtpCZCUyGb9f0bmQww3i1iEGHp/9r+Af5iZSWoELDSzUkmDgPwUZO7BuVXGmVlJ2ZNm9qTXGgSMTLFe7cxsNjBbUndcr3sCcL2kh8xspaSWwPpK3DQps2zpMq6/6iZKS0opLS3l0CN7c8DBPdKV3UBBQQGXXzmM8wYPpbS0lP7H96N9h3ZBfwvRz1QZD/7pPubN+oyffljJ9b+6miPOOJr9+nSP3JUC2WmjisjLuqMkNVSejykXkFQCzMa5ToqBccBfveHuAPwHNxD5GvA7M6svqQ3wnJl1ltQLuNTM+nq9WsAyYF8z+8gfGwGsNLPb/P7ewMPAL3AumOfM7AlJXwDdzGyppG7AbWbWS9KdQG9c7/wD4EwzWyvpQlwPHWAlMNDM5lX0WZet/Tajf4R6BeVHDgQCqbIlrCe+bX6jtKzwvB8/Svl72q5hx6xZ/Jw14lHjje/tZpal316pE4x4INcJRhzmr0jdiO/SIHtGPBbulHSRdBlwHhsjVAKBQKCa5KY7JTYDm+lgZjeb2c5m9mZN1yUQCMSTXI1O2Sp64oFAIJAuIVFyIBAIxJgol7iIkmDEA4FAIAVCTzwQCATiTG7a8GDEc4EQAhjIdTIdArjwp68zqg/QvmGjtO4PPfFAIBCIMcGIBwKBQIwJA5uBQCAQY0JPPBAIBGJMbprwYMQDgUAgNYI7JRAIBOJLcKcEAoFAjMlNEx6MeCAQCKRErkanbBWrGG5JTH5jCscePYC+R57AmNFjg37Qz7kyMq3/1MPPcN7JQ/ntKUP585V/Yd3adZGXUR65uoph7Iy4pBJJMyTNlTRT0iWSKv0cknaU9IR/30vSc1Vcv5ukib6cDyWNSqFeb1Xvk1SfkpISbrzhFu4eeQdPPfso41+YwLzPost2H/S3bP1slJFp/aVFy3j20ef52wO3cvejf6e0tJTXX8rOCtPBiEfHap+AeHfgcOBoYHhlN5jZN2Y2oBpl/B2XBairmf0CuLOqG8wsumSXFTBn9lxa79SKVq1bUqt2LY7qcwQTX50U9IN+zpSRjc9QUlzCurXrKCkuYe2atWzfrEmk+nEjjkZ8Az758BDgAjnaSHpD0nS/9QDwx+ck3yspT9KnPglzYv8zSU2BHYCFSeXM9tecKelpSeMlfSxpeJLeSv/ay/fin5D0kaSHFJEzrWjxElq0aL5hv7BFIYuLlkQhHfS3Av1slJFp/aaF23PCwOM4s98QBvY5m3r16rHX/l0j068MSSlv2STWRhzAzObjPkchUAQcbmZ7AafgetQV3VcKPMjGlG2HATPNbClwO/CqpBclXSxpu6Rb9/X3dAVO8rk7y7IncBHQCdgFOKDsBZKGSJomadqY0fen+ll/dizK/y5Bf8vWz0YZmdZf8eNKpk56h3uf/hfjXhzDmjVrePWFiRGWUDHBnZJZEq1WCxgtaTbwOM6IVsa9wBn+/dnAfQBmdh8u4/3jQC9gqqRt/HUvm9kyM1sNPAn0LEf3HTNb6B8UM4A2ZS8ws1Fm1s3Mup0z+MxUPiPNWxSyaNHiDftFi4ooLGyW0r1BP+hno4xM6894ZybNd2xOo8aNKCgooEfv/flw1seR6VdG6IlnCEm7ACW4XvjFwGKgC9ANqF3ZvWa2AFgs6RBgP+DFpHPfmNm9ZnYcUAx0TpwqK1OO9Nqk9yVEFMq5e+dOfPXlAhYu/Jr169Yz/sWXOLj3gVFIB/2tQD8bZWRav1mLZnw8+xPWrFmLmTHz3Vm0btsqMv3KyNWeeKzjxL0/+1/AP8zMJDUCFppZqaRBQH4KMvfg3CrjzKzE6x4FvGJm6yW1ALYHvgZ+CRwuqQmwGuiP68FnhYKCAi6/chjnDR5KaWkp/Y/vR/sO7YJ+0M+ZMjKt37HzrhxwaHcuHPh78vPz2GW3Xehz/BGR6VdGbkaJg8rzYeUykkqA2TjXSTEwDvirN9wdgP8Aq4DXgN+ZWX1JbYDnzKyzpF7ApWbW1+vVApYB+5rZR/7YX4FjgDW+2FvN7EFJZ+KiYeoB7YGHzexaf89KX1ZZ/X8A08zs/oo+05qSH+L1RwgEIiY7SSE6pWWHf1y3JOXvacPazbJm82NnxKPGD0zebmZV/ubzRrybmV0QZR2CEQ9s7cTCiK9fmroRr9U0a0Y81u6UdJF0GXAeGyNUAoFAoFzyctShstX3xHOB0BMPbO3EoSe+svi7lL+n9QuahJ54IBAI5BK52Q/fAkIMA4FAIBtEHWIo6Sg/8/sz79rdLIIRDwQCgRSI0ohLygfuAvrgJiX+SlJVkxPLJRjxQCAQSAVVY6uafYHPzGy+ma0D/g0ctznVCj7xHGDb/EbVcrdJGmJmVS6Pu7nEXT8bZQT9aPXbN2yUUf0oqJO/XcrfU0lDcIvzJRhVpr4tgQVJ+wtxs8arTeiJx5MhVV+yVetno4ygv2Xrp0Xy2kh+K/vAKe+BsFlRasGIBwKBQPZZCLRO2m8FfLM5QsGIBwKBQPZ5F+ggqa2k2sCpwDObIxR84vEk077AuOtno4ygv2XrZxQzK5Z0ATABt1DfvWY2d3O0wozNQCAQiDHBnRIIBAIxJhjxQCAQiDHBiAcCgUCMCUY8EAgEYkyITokRfr2F5iT93czsq4i0dwX+CTT3GZD2AI41sxsi0j8AGAHsjKu/ADOzXaLQ92VsA5yIS0yd3EbXRVjGzkAHM/ufpDpAgZmtiFC/JRvbCAAzmxSRdjNgMD9vn8hSDErqiWuf+3x59c3s8wj1e/Dz+j8QlX4cCdEpMUHS74DhuETQpf6wmdkeEem/DgwDRprZnv7YHDPrXPmdKet/hEtk/R4ueTQAZrYsCn1fxnjgh3LK+EtE+oNxMwWbmFk7nw7wX2Z2aET6fwZOAT5gY/3NzI6NSP8t4A1+3j7/iUh/OC5B+W5mtqukHYHHzeyAiPTHAe2AGWzaPkOj0I8roSceHy7EfTkiM3plqGtm70ibzAYujlD/BzN7MUK98mhlZkdlUP983MJFbwOY2aeSCiPU74/7G6+NUDOZumb2xwxpAxwP7AlMBzCzbyQ1iFC/G9DJQs9zE4JPPD4swPUyM8VSSe3w6zdIGgB8G6H+a5JuldRd0l6JLUJ9gLck/TJizWTW+hXnAJBUwGaud1EB83EJwDPFc5KOzqD+Om9gE/+H6kWsPwdoEbFm7Ak98RxH0iX+7XxgoqTngQ09NTP7a0RFnY+bBddR0tfA58DAiLRh4wpt3ZKOGXBIusKSZnutAuAsSfNxbZTwu0ficgJel3QFUEfS4cBvgWcj0gZYBcyQ9Aqb/o3TchdIWoFrHwFXSFoLrGdj+zRMRz+JxySNBLbzrqezgdERaQM0BT6Q9A6btk8k7qa4EnziOY73M1aImV0bcXn1gLwoB+syjR9srBAz+zKicvKAc4AjcAZwAnBPVD/vJQ0q77iZjY1CPxv4h9uG9jGzlyPUPri842b2elRlxJFgxGOINyb1zezHCDUvBO4DVuB6T3sBl5nZS2nqDjSzB5N+UWxChL8k8O6ghWa2VlIvYA/gATNbHlUZSWU1wfngZ0WtnSl8hNAMM/tJ0kDc3/hvEUY41QPWmFmJpN2A3YAXzWx9FPqB8gk+8Zgg6WFJDf0X5QPgY0nDIizibP9QOAIoBM4Cbo5AN+EXbVDBFiX/AUoktQfGAG2Bh6MSlzTR/w2a4CIk7pOU9kNI0mP+dbakWWW3dPWT+CewSlIX4A/Al8C4CPUnAdv4MMn/4f4P3Z+uqKQ3/esKST8mbSskRdaRiSvBJx4fOpnZj5JOB14A/ogLFbs1Iv1EWMrRwH1mNlNlQlU2BzMb6V8jdftUQKlfHe4EXA/zTknvR6jfyP8NzsW10fCIjOyF/rVvBFqVUWxmJuk44A4zG1ORC2czkZmtknQOcKeZ3RJF+5tZT/8a9UN/iyD0xONDLUm1cGFoT/ufqFH6wt6T9BLOiE/woWGlVdyTMpJ2kfSspCWSiiQ9LSmyiT6e9ZJ+BZwBPOePRRntUSBpB+DkJP20MbNv/euXuAG7LjhX0Nqo/PmeFZIuxw1YP+8nj0XZPpLUHTgdeN4fi7Sj6KOahkr6naQ9o9SOK8GIx4eRwBc498QkP5gX5U/Jc4DLgH3MbBVQG/dzOCoeBh4DdgB2BB4HHolQH1x9uwN/MrPPJbUFHoxQ/zrcYOZnZvaufwh9GpW47+G/A5wADACmSopsNiVuItFa4BwzW4TL8xjVLzlwvyguB54ys7m+fV6LSlzSNcBYYHtcpMr9kq6KSj+uhIHNGCOpwMwim5CjzE75ftvM9itzbKqZ7R+F/paApI+BHokJXZK2B94ys91qtma5gaQPgT3NbI3frwNMN7Nf1GzNapbgE48Rko4Bdge2TTocybogFU35xg1WRcFrki4D/u11T8H9pG8CYGbfpVuA3DT4m4BOJLVRVOuzKPNrjyzERQclWMGmGdHTQtL+wJ3AL3C/tPKBlWZWvVTzFevvClzKz9sn7bkAni9wf9c1fn8bYF5E2rEl9MRjgqR/AXWB3sA9uJ/b75jZORHpfwzskakp35IqWwQpkoWwfBTDcOB2oB/OvSIzqzTWvhr6mV575AHgl8DTuAfdcTj3yie+nLQiYSRNw+VyfBw36eoM3GJVV6Sjm6Q/E/gXP2+f9yLS/y+wD/Ayrn0OB94Einw5W+UaKqEnHh96mNkekmaZ2bWS/gI8GaF+Ysp3Roy4mbXNhG4Z6pjZK5LkBwRHSHoDZ9ijINNrj8xj057l0/41sqgMM/tMUr6ZleBCJN+KShsX/fLPCPXK8pTfEkzMYFmxIRjx+LDav66SWx1uGS4OOioyNeV7H2CBH0hD0hm45WK/BEZE4UZJYo2fCPWpXBLar3Ex71HxnKSjzeyFCDU3kByGKakxsDzixZ5WyWVWnyHpFtzaOFGub/KspN/iDG3y/6FI/saJmas+Sqsz8LWZFUWhHWeCOyUmSLoa5888FLgL93NytJldE5F+RqZ8S5oOHGZm30k6COcT/x3QFfiFmQ1IR79MWfsAHwLbAdcDjYBbzGxqRPorcEYv0rVHfNTFY2b2kdya6C/i2qcYOM3M/pdWxTeWszNuKePauGWBGwF3m9lnEemX5zJL21XmXYl3+oiXRsAUnLumCXCpmUUd5RQrghHPcSRdBEwG3k9Eovgv+rZmlslVDSNB0kwz6+Lf3wUsMbMRfn+GmXWNogycb/QtYLKZfZGuZjaRNBfo7CfiDAFOwz2sdwXGmtm+aeovA6bi2wc3lrIqzWpnDUlzzWx3//4ioJeZ9ZfUAjetf6uOFw/ulNynFXAHbnXBWWz8Ik6JQlzSY2Z2sjauBLgJlv4KgPlJoZCH4pIqJIjq/9/pQA/cQNdwuaUJ3kpsZvZ2OuKSOvpecrlL55rZ9HT02biEK8CRwCPeZ/2h3HK36dIW2B/XRlcAe8ut9Jh46D2WjrikQ8zsVT9T9meYWbpjN+uS3h+OG5jFzBZFMKk49oSeeEzwvsxuuC9id78tN7NOaeruYGbfqoKVANOdMSjpStws0KXATsBevsfZHtfLjCTrS5kym+KiMC4C2ppZfpp6o8xsiKTyJq5YuiF0kqYC5+JcHR8De5tPaSbpIzPrmI5+OeXVw0XuXEQ07XOtX4LgvnJOW7ohmL7d/wJ8A7wKdPQGvACYE3X7xI3QE48PdYCGOD9mI9x/6NnpipaZ8r0BPyX7VNwAZDr6f/KDpTsALyX1OPNwvvG08XXdE/eAOwCXwutrXChm2r9YzGyIf+2drlYFXAg8ATQDbk8y4EcDaa894gfCe/htH3/4PeAqommf4f41yhm+yfwa+DsuIcRFiUFy3C+75yu8aysh9MRzHEmjcBN8VuDSgk0FpprZ9xHpN8QlhGgJPIOLwb0AN2ljhpkdF0U5vqyMJHqW9BNuQPMuYKJFmJi3inIPB/5gZodno7zNRVIpLmXa7bicl+uquGVzysgHGpvZUr9fGzgTuDiqGZWSmkQczbRFEIx4jiOX/LcpLjXVW7ie05yoQs8kPQ1873UPBRrjohcuNLMZUZThy7kAl+0+8kTPcotedQf2xkUtvIv7PFPM7OsI9A/BTWLZEfgvcCPwAC465U8R+HwT5ZS35voPwHvp/C3kFqXqjuuJt8XNfJzit2npTvCSdCpubZ+fcGvJjMAtcfsucH0EYwaJcj7FLwGMG9AMxotgxGOB3OjN7mz8SdwZ+A5npNKayCJptpn90r/Px/uuLeLMPpI+A/azzCV6TpRTF5fM+ABcT7C2mVWa+ScFzfdxIXlTgD44A361md2RXm1/Vs7DuHGPRMq3Y3CGsCOuB31LROW0wc1ovRCX2GLbyu+oUm8O0N9PJNoL106nmtlTVdxa3XIEHIZL+7Yv8Chwv5l9EmU5cSMY8RghqRXOOPXArT29vZltl6bmdDPbq6L9qPCDU4dbhAt2ldGvh8vjmfCL74Nbd2SymV2QpnbZNppnZu3S0aygnAnAiWa20u/Xx/nKj8f1xjd7EFtSRzZ2Ag7A/eKagmuf29Ksd9n2iXwwtpwye+NWqKwHzMRloYokYituhIHNHEfSUDZ+8dazMbzwXiIY2AS6aGN2FOGSAP9IRBNZkshYomffU94JmIZzOf0FN26wMl1tz3ZlwueUvB+VOwX3GZL91euBnc1stVxy481C0lLc7MzE2i83RzXBx1NYxhVUP3k/ir8xbFjVcSDwfzi33O9w4zhdcWGH2VjaIecIRjz3aYPrjV2ciCSJknTDy6rBV36r7bcoGQTMzqCP9HWc+6G8fSO6NWwexq0hnlgzpR/wiDam5Ntc2pnZDxkcGBzNpuu7lN2Piik4X3t/M1uYdHyan9W5VRLcKTEiU9Ed2dL3ZTRwspH1kpO1MzIwmE0kdcP96hLwpplNi1A7owODmY4ekaQwmPlzghGPCZJ+h1uNL/Lojizpd8b1opr4Q0uBM8xsbhT6voyMDgxm4yGRyQdppgcGM/WQkPQslaQiNLNjoygnrgQjHhMyHd2RBf23gCvN7DW/3wu40cx6RFhGxgYGvV6mHxLJD9ISNo5LRPIgLVNW5AODmXpISDq4svNm9no6+rHHzMIWgw2Xq7AgxvozUzmWZhkf4kIKE/vbAB/69+9HoD8BqJ+0Xx8Yj5tN+0EE+p/hIo4y9TfYHhdWOA030/EEXI+/G/B5xGX1xs2aXY4bQ+ieqc+1tW9hYDM+ZCy6I1v6csvpjvP7A4GoZ1ZmamAwQUaiR5JYgHPPZIqMDgxmKnpEmV+kLdYEIx4fMhndkQ39s4Fr2RjJMQk3GScyzOx6SS+ycWDwN7ZxYPD0CIrI9EMi0w/S3cx3k8tiZn+OQD9TD4kL/WvfNDS2WIJPPGZkMrojk/qSTjKzx6s6FkE5mY7gyWT0SLmzby0p489m6mZlYDBEj9QMwYjHhExHd2RB/2czQaOeHZqNgcFshGFGTaYHBrP4kNgfl93qF7hfi/nATxbdhLRYEtwp8WEUcIltGt0xGjebM2f1JfXBrSfeUtLfk041xKUfi5ILcS6DTEXYlPuQANJ6SEj6m5ldVJExTNcIpmukUyCtafvV4B+45ZEfxw3GngG0z1LZOUsw4vGhXsLAApjZRO+LzXX9b3DREMfi1rBOsAK3qFSUZHpgMFMPicRgb0aMYaYHBrPwkEgu6zNJ+eYyH93nQ1e3aoIRjw+Zju7IiL6ZzQRmSmpuZZIuS7oQl3ouKjI9MJiRh4SZvedfM2UMMzowmMXokVVy65TPkHQLbj2YKDsysST4xGOCpMa46I6e/tAkYISZLY+Jfnk+8fctwiS3mRoYTNIfA+yGi7GO7CFRkfFL0s/pEDplOMVfUjk741xZtXG/4hoBd1u0i3nFjtATjw+HmdnQ5AOSTsInjc1VfbmEDacBbSU9k3SqARCpWyIqY10JmQrDTPSQz/eviV9DpwORZaXP1MCgVZDiL2rM7EtJzfz7TP+tY0PoiceETEd3ZErf957aAjcBlyWdWgHMsgjWF8/0wGC2kDTZyiSOLu9YGvrTKGdg0MyujEg/Iw8JP51/OC5toHD5WYuBO83surQqvQUQeuI5TqajOzKt73tnXwLdJTVnY6LeD6Mw4J5MDwxm6yFRT1JPM3vTl9uDiH2+GR4YzFT0yEX4RB+2MYn0LsA/JV1sZrdHUEZsCUY898l0dEdWoke8a+Y2YCKuN3WnpGFm9kS62lkYGMzoQyKJc4B7JTXy+8txM12jIuMDgxl6SJyBywq1NKmc+ZIGAi/hEkBvtQR3SkyQ9Acrs0qepAstojyPWdCfifsiFvn9ZsD/zKxLBNqxHhgsi6SGuO9mpJEwmR4YlDQJt4rhPcAi3EPizHT/xpLmmFnn6p7bWghGPCZkOrojC/obEjL7/TzcKoa/rOS2VLUTURHlDgym6zfN9ENC5a9TnqwfVYhk4uGJmS2JSjNJOyMPicrGZqKe9RtHgjslx8l0dEcWo0fGy633/YjfPwV4IQrhRFSEpAPKDAJeJmkykO7gV6ajRxKpzHbDjRkk/g79cKGeaVHewKCkyAcGMxg9kpwHNhkB20ZYTiwJRjz3eQv3s7QpLgFwghXArFzXl9QeaG5mw+SSC/fEffmmAA+lq1+GjAwMZvohkTB4kl4C9jKzFX5/BNGEkF5EBgcGM/2QsOzlgY0lwZ0SI8pEd7yT8C/nsr6k54ArzGxWmePdgOFm1q/8OzerrL2Be3E/48EPDJrZ9Ij0ZwAXlHlI3G1mXSPS/wjoYmZr/f42OJdTxzR136fMwKA/3gx4KV2XmaSLcRFOQ8o+JIDxW3v0SKYJRjwmlBPdcSAQSXRHJvWrGJTaxE8eFRkcGMz0Q+JK4GTgKZwP/njgMTO7MU3djA4MZvohEaicYMRjQiajOzKpL+kzMys3Vriyc9UsI2sDg768jDwkvPZeuAcowCQzez8CzYwODIbokZol+MTjQ14Z98Yy3My1XNd/V9JgMxudfFDSOWwal54OmR4YLPch4VzBkT8k6gI/mtl9kppJaptwUaRBpgcG123muUAEBCMeHzIW3ZFh/YuApySdzkaj3Q0XhnZ8BPrZGBjM6EMigdwCXt18OfcBtXAZ6dOadp+FgcEQPVKDBHdKjpMU3TG5THTH98BDZjYvl/WTyukNJH5WzzWzV6PQLVNGRgYGk/RfAk5Mekg0AB43s6Mi0p8B7AlMT/iRJc2K22SlQHYJPfHc52/AFQBm9iQ+0bCP7vgbrjeYy/p47deA16q8MD3GAe9ISh4YfCBC/bLZ7tcBbSLUX2dmJskAFG3Sj8AWSjDiuU+bsuF5AGY2TVKbGOhnDTP7k1y2+8TA4FlRDAwmkemHxGOSRgLbSRqMWzdldBX3BLZygjslx8l0dEc2okeyiaSeQIfEwCBQP4KBwWT9yKNHyugfDhyBc2lNMLOXo9QPbHlEGd0QyAzv+l7ZJkQY3ZFp/azhBwb/CFzuDyUGBqMkET1yB7BQUtsoxc3sZTMbBtwM/C9K7cCWSeiJ5zh+FuVTOP/rz6I7zGxRLutnk0wPDCZHj5jZrpJ2xA1sphU9IpdM4WbgO+B6nNumKa6TdYaZjU+v5oEtmeATz3HMbDHQo0x0x/NRRXdkWj/LZHpg8Hj8QwLAzL7xESrp8g/c4HIj4FWgj5lNldQRF/IZjHigQoIRjwmZju7IUvRIpsn0wGCmHhIFZvaS17zOzKYCmNlHiQlFgUBFBCMe2GIws9v8wOCPuAkz10Q8MJiph0Rp0vvVZc4Ff2egUoJPPLBFIqkpsMwi/g+eiegRSSXAT16zDhvXKBewrZnVSreMwJZLMOKB2FMTA4OZekgEAtUlhBgGtgT+AdyIGwR8FTjXzFoABwE3pSsuaX9JEyU9KWlPSXOAOcBiSZFMuQ8ENpfQEw/EHkkzEokZJH1oZr9IOvd+BEkPprExemQUZaJHwnrZgZok9MQDWwKZHhgsMLOXzOxxYFFy9EgE2oFAWoTolMCWQGIpVAF1kpZFjWop1BA9EshZgjslEKiCED0SyGWCEQ8EAoEYE3zigUAgEGOCEQ8EAoEYE4x4IBAIxJhgxAOBQCDG/D+/tvmJOROzRgAAAABJRU5ErkJggg==\n", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["from sklearn.metrics import confusion_matrix\n", "\n", "# Determine the accuracy of the model\n", "score = knn.score(X_test_sc, y_test)\n", "\n", "# Provide the necessary label\n", "class_label=['CottonWash', 'CottonRinse', 'CottonSpin', 'BeddingWash', 'BeddingRinse', 'BeddingSpin',\n", "                 'DailyWash', 'DailyRinse','DailySpin']\n", "\n", "knn.predict(X_test_sc)\n", "cm = confusion_matrix(y_test, knn.predict(X_test_sc), labels=class_label)\n", "#\n", "axes = sns.heatmap(cm, square=True, annot=True, fmt ='d', cbar=True, cmap=plt.cm.GnBu)\n", "axes.set_ylabel('Actual')\n", "axes.set_ylabel('Predication')\n", "tick_marks = np.arange(len(class_label)) + 0.5\n", "axes.set_xticks(tick_marks)\n", "axes.set_xticklabels(class_label, rotation = 90)\n", "axes.set_yticks(tick_marks)\n", "axes.set_yticklabels(class_label, rotation = 0)\n", "axes.set_title('Confusion Matrix')\n", "plt.show()"]}, {"cell_type": "markdown", "id": "f83d9fa7", "metadata": {"id": "f83d9fa7"}, "source": ["What can you conclude from the confusion matrix?"]}, {"cell_type": "markdown", "id": "d96f6cd7", "metadata": {"id": "d96f6cd7"}, "source": ["Your response here ..."]}, {"cell_type": "markdown", "id": "92485181", "metadata": {"id": "92485181"}, "source": ["There are some metrics that we can calculate to make more sense of these numbers above. The following are some of the most important metrics we can get from the quantities observed in the confusion matrix:\n", "\n", "- Accuracy: Proportion of cases correctly identified by the classifier (N represents the total number of observation in the testing dataset):\n", "\n", "![accuracy.png](attachment:accuracy.png)"]}, {"cell_type": "markdown", "id": "2968d895", "metadata": {"id": "2968d895"}, "source": ["- Precision: Proportion of correct positive predictions. In this problem, this is the proportion of cases when the model is correct when it predicts the mode of the washing machine:\n", "\n", "![precision.png](attachment:precision.png)\n"]}, {"cell_type": "markdown", "id": "307be229", "metadata": {"id": "307be229"}, "source": ["- Recall (or Sensitivity): Proportion of observed positives that were predicted correctly as positives. In this problem, this is the proportion of actual mode that the model can correctly identify:\n", "\n", "![recall.png](attachment:recall.png)\n"]}, {"cell_type": "markdown", "id": "ef70f487", "metadata": {"id": "ef70f487"}, "source": ["- F1 Score (or Balanced F-score or F-measure): The score can be interpreted as a harmonic mean of the precision and recall, where an F1 score reaches its best value at 1 and worst score at 0. The relative contribution of precision and recall to the F1 score are equal. The formula for the F1 score is:\n", "\n", "![f1_score.png](attachment:f1_score.png)\n"]}, {"cell_type": "markdown", "id": "e4e15499", "metadata": {"id": "e4e15499"}, "source": ["These 4 metrics will help us understand how good is your model. So, let's calculate the accuracy, precision, recall and F1 Score for our KNN model using the test dataset with k=6 as it's hyperparameter."]}, {"cell_type": "code", "execution_count": null, "id": "a424cca3", "metadata": {"id": "a424cca3", "outputId": "b6913a11-d050-4525-bdfa-eb48ce3eddbd"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Accuracy: 83.7%, Precision: 85.9%, Recall: 87.3%, F1 Score: 86.1%\n"]}], "source": ["from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score\n", "\n", "knn = KNeighborsClassifier(n_neighbors=??)\n", "knn.fit(X_train_sc, y_train)\n", "\n", "accuracy = accuracy_score(y_test, y_pred_test)\n", "precision = precision_score(y_test, y_pred_test, average='macro')\n", "recall = recall_score(y_test, y_pred_test, average='macro')\n", "f1 = f1_score(y_test, y_pred_test, average='macro')\n", "\n", "print (\"Accuracy: {:0.1f}%, Precision: {:0.1f}%, Recall: {:0.1f}%, F1 Score: {:0.1f}%\".format(100*accuracy, 100* precision, 100*recall, 100*f1))"]}, {"cell_type": "markdown", "id": "3210c9a4", "metadata": {"id": "3210c9a4"}, "source": ["When the KNN model makes a positive prediction on the mode of the washing machine, it is correct around 86% of the time (precision). This result is not too bad. In addition, the model is also able to identify the mode of the washing machine correctly for 84% of the time.\n", "\n", "What is the reason for using the test dataset to compute your performance matrices?"]}, {"cell_type": "markdown", "id": "2cf1bcde", "metadata": {"id": "2cf1bcde"}, "source": ["Your response here ..."]}, {"cell_type": "markdown", "id": "cab8958b", "metadata": {"id": "cab8958b"}, "source": ["Before we proceed to build the next predictive model, you may save your KNN model for future comparison."]}, {"cell_type": "code", "execution_count": null, "id": "29e54a70", "metadata": {"id": "29e54a70", "outputId": "65ece7e1-7acf-4120-9cc5-c5e2a2a70382"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model Saved\n"]}], "source": ["import pickle as pk\n", "\n", "model_filename = ROOT_DIR + \"/model/knn.mdl\"\n", "with open(model_filename, \"wb\") as file:\n", "    pk.dump(??, file)\n", "print(\"Model Saved\")"]}, {"cell_type": "markdown", "id": "fc84f0d3", "metadata": {"id": "fc84f0d3"}, "source": ["\n", "\n", "***"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}, "colab": {"provenance": []}}, "nbformat": 4, "nbformat_minor": 5}