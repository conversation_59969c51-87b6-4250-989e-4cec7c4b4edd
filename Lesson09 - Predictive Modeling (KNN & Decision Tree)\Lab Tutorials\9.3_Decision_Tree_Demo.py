#!/usr/bin/env python
# coding: utf-8

# # Lab Demonstration - Decision Tree
# ****
# 
# Classification trees are popular because they are transparent, straightforward and easy to understand how they produce the predictions. They can be used for regression or classification tasks. They produce the predictions by creating a series of rules that applied consecutively until they arrive at a "leaf" node in the tree that contains the classification.
# 
# ![DecisionTree.png](attachment:DecisionTree.png)

# 
# ## Decision Tree Classifier
# 
# ### Defining Dataset
# 
# Let's create a dataset with two features (in blue ) and one label (in orange) that contain a record of weather, temperature and the decision to play tennis from the past 14 days. This dataset will be used to build a model to predict whether the weather and temperature today is suitable for you to play a tennis match outdoor.
# 
# ![table-3.png](attachment:table-3.png)
# 

# In[1]:

# Note: Google Colab imports removed for local execution

# In[2]:

# Note: ROOT_DIR not needed for local execution

# In[3]:


# First Feature
weather = ['Sunny','Sunny','Overcast','Rainy','Rainy','Rainy','Overcast','Sunny','Sunny',
'Rainy','<PERSON>','Overcast','Overcast','Rainy']

# Second Feature
temp = ['Hot','Hot','Hot','Mild','Cool','Cool','Cool','Mild','Cool','Mild','Mild','Mild','Hot','Mild']

# Label or target varible
tennis = ['No','No','Yes','Yes','Yes','No','Yes','No','Yes','Yes','Yes','Yes','Yes','No']


# ### Encoding data columns
# 
# Various machine learning algorithms require numerical input data, so you need to represent categorical columns in a numerical column.
# 
# In order to encode this data, you could map each value to a number. e.g. Overcast:0, Rainy:1, and Sunny:2.
# 
# This process is known as label encoding, and $sklearn$ conveniently will do this for you using $Label Encoder$.
# 

# In[ ]:


# Importing preprocessing library
from sklearn import preprocessing

# Creating a labelEncoder
le = preprocessing.LabelEncoder()

# Converting string labels into numbers.
weather_encoded = le.fit_transform(weather)
print (weather_encoded)


# Here, you have imported preprocessing module and created Label Encoder object. Using this $LabelEncoder$ object, you can fit and transform "weather" column into the numeric column. Similarly, you can encode temperature and label into numeric columns.
# 

# In[ ]:


# Converting string labels into numbers
temp_encoded = le.fit_transform(temp)
label = le.fit_transform(tennis)


# ### Combining Features
# 
# You can combine multiple columns or features into a single set of data using "zip" function.
# 

# In[ ]:


# Combining weather and temp into a single list of tuples
features = list(zip(weather_encoded, temp_encoded))


# ### Generating Model
# 
# Let's build Decision Tree classifier model.
# 
# First, import the $from sklearn.tree import DecisionTreeClassifier$ module and set the $max depth$ parameter to 3.
# 

# In[ ]:


from sklearn.tree import DecisionTreeClassifier
class_tree = DecisionTreeClassifier(max_depth=3)
class_tree.fit(features, label)


# We start vistualizing the classification tree by importing 'export_graphviz', which exports the decision tree in a file with DOT format. This function generates a GraphiViz representation of the decision tree, which is then written into 'out_file'. Finally, the image function is used to display the tree. You may refer to https://www.graphviz.org/ for more information on GraphViz.
# 

# In[ ]:


from sklearn.tree import export_graphviz
import matplotlib.pyplot as plt
from sklearn import tree
import os


# Get the directory where this script is located
script_dir = os.path.dirname(os.path.abspath(__file__))

# The code to display the graphical representation is as follows:

# In[ ]:

# Display decision tree using matplotlib (works in local environment)
plt.figure(figsize=(12, 8))
tree.plot_tree(class_tree,
               feature_names=["weather_encoded", "temp_encoded"],
               class_names=["Play", "Don't Play"],
               rounded=True,
               filled=True)
plt.title("Decision Tree Visualization")
output_path = os.path.join(script_dir, "decision_tree_full.png")
plt.savefig(output_path, dpi=300, bbox_inches='tight')
print(f"Decision tree visualization saved as '{output_path}'")
plt.show()

# Alternative: Export to text format
print("\nDecision Tree Rules (Text Format):")
tree_rules = export_graphviz(class_tree,
                            feature_names=["weather_encoded", "temp_encoded"],
                            class_names=["Play", "Don't Play"],
                            filled=True,
                            rounded=True,
                            special_characters=True)
print("Decision tree exported. You can visualize it using graphviz tools if needed.")


# The above diagram generated is the graphical representation of the rules produced by the $Decision Tree$ model. For every observation, start at the top and based on the truth value of the proposition, go left if it is True and right if it is False. Suppose you have the data for one observation, then you start answering in the following sequence:
# 
# 1. Is weather_encoded <= 0.5? Go right if the answer is False
# 2. Is temp_encoded <011.5? Go left if the answer is True
# 3. Is weather_encoded <= 1.5? If the answer is False, go right and predict that the weather and temperature is bad, so you may advice the players not to play tennis.
# 
# Next, you may find it more useful to print the 'proportions' instead of the 'counts' by setting the parameter for $proportion=True$.

# In[ ]:

# Display decision tree with proportions using matplotlib
plt.figure(figsize=(12, 8))
tree.plot_tree(class_tree,
               feature_names=["weather_encoded", "temp_encoded"],
               class_names=["Play", "Don't Play"],
               rounded=True,
               filled=True,
               proportion=True)
plt.title("Decision Tree Visualization (with Proportions)")
output_path = os.path.join(script_dir, "decision_tree_proportions.png")
plt.savefig(output_path, dpi=300, bbox_inches='tight')
print(f"Decision tree with proportions saved as '{output_path}'")
plt.show()


# As you can see in the leftmost leaf node, only 14.3% of the samples are 'Play', compared to the rightmost node where 35.7% are 'Don't Play'.
# 
# Let's modify your Python code by assigning the $ max depth$ of tree to 1
# 

# In[ ]:


# Rewrite your Python code here...
# Create a new decision tree with max_depth=1 for comparison
class_tree_depth1 = DecisionTreeClassifier(max_depth=1)
class_tree_depth1.fit(features, label)

# Display the simplified decision tree
plt.figure(figsize=(10, 6))
tree.plot_tree(class_tree_depth1,
               feature_names=["weather_encoded", "temp_encoded"],
               class_names=["Play", "Don't Play"],
               rounded=True,
               filled=True,
               proportion=True)
plt.title("Decision Tree Visualization (Max Depth = 1)")
output_path = os.path.join(script_dir, "decision_tree_depth1.png")
plt.savefig(output_path, dpi=300, bbox_inches='tight')
print(f"Decision tree with max depth=1 saved as '{output_path}'")
plt.show()


# ### The Pros and Cons of using Decision Trees
# 
# #### Advantages
# - They are very easy to understand and explain
# - The rules produced are easy to implement
# - It is computationally efficient to produce predictions with them
# - As shown above, there is very little pre-processing needed. The classification trees are not affected if the predictors are skewed (unbalanced), or being in different scales.
# 
# #### Drawbacks
# - Their predictive power is often less than that of other models. Hence, you would not expect highly accurate predictions.
# - They may be unstable. For example, small changes in the dataset can lead to very different rules.
# - They can be overfit.
# - Due to the simplicity of the if-else rules, there are some complex interactions that these models cannot learn.
# 
# You may refer to https://scikit-learn.org/stable/modules/tree.html#tree for some useful tips on practical usage.
# 

# Assume today's weather is overcast and the temperature is mild, let's test the outcome of the Decision Tree model to predict wether you should still play tennis today.

# In[ ]:


# Predict the Output
predicted = class_tree.predict([[0, 2]]) # 0:Overcast, 2:Mild
print (predicted)


# In the above demonstration, you have given input [0, 2], where 0 means Overcast weather and 2 means Mild temperature. The Decision Tree model has predicts [1], which means today's weather and temperature is suitable for you to play tennis.
# 
'''
To run the codes;
python "Lesson09 - Analysis Models/Lab Tutorials/9.3_Decision_Tree_Demo.py"
'''