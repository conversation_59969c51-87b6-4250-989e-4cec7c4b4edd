#!/usr/bin/env python
# coding: utf-8

"""
File Verification Script for KNN Lab Tutorial
This script verifies that all generated files from 9.2_KNN.py are present
and provides information about their sizes and creation times.
"""

import os
import pickle
from datetime import datetime

def verify_files():
    """Verify all generated files from the KNN tutorial"""
    
    # Get script directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Define expected files
    expected_files = {
        'KNN Accuracy Plot': 'knn_accuracy_comparison.png',
        'Confusion Matrix Plot': 'knn_confusion_matrix.png',
        'Trained Model': os.path.join('model', 'knn.mdl')
    }
    
    print("="*70)
    print("KNN LAB TUTORIAL - GENERATED FILES VERIFICATION")
    print("="*70)
    print(f"Script Directory: {script_dir}")
    print("-"*70)
    
    all_files_exist = True
    
    for file_type, filename in expected_files.items():
        file_path = os.path.join(script_dir, filename)
        
        if os.path.exists(file_path):
            # Get file stats
            stat = os.stat(file_path)
            size_kb = stat.st_size / 1024
            modified_time = datetime.fromtimestamp(stat.st_mtime)
            
            print(f"✓ {file_type}:")
            print(f"  Path: {filename}")
            print(f"  Size: {size_kb:.2f} KB")
            print(f"  Modified: {modified_time.strftime('%Y-%m-%d %H:%M:%S')}")
            
            # Additional info for model file
            if filename.endswith('.mdl'):
                try:
                    with open(file_path, 'rb') as f:
                        model = pickle.load(f)
                    print(f"  Model Type: {type(model).__name__}")
                    print(f"  Model Parameters: n_neighbors={model.n_neighbors}")
                except Exception as e:
                    print(f"  Model Load Error: {e}")
            
            print()
        else:
            print(f"✗ {file_type}: NOT FOUND")
            print(f"  Expected Path: {filename}")
            print()
            all_files_exist = False
    
    print("-"*70)
    if all_files_exist:
        print("✓ ALL FILES SUCCESSFULLY GENERATED AND VERIFIED!")
        print("✓ All files are stored in the same directory as the source code.")
    else:
        print("✗ SOME FILES ARE MISSING!")
        print("  Please run 9.2_KNN.py to generate the missing files.")
    
    print("="*70)
    
    return all_files_exist

if __name__ == "__main__":
    verify_files()
