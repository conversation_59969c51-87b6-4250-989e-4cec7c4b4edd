#!/usr/bin/env python
# coding: utf-8

# # C379 Emerging Technologies

# # Lesson 8 - Scientific Computing Library for Python 

# In[ ]:


# from google.colab import drive
# drive.mount('/content/drive', force_remount=True)

# In[ ]:

# ROOT_DIR = '/content/drive/MyDrive/C379'


# ### Using NumPy 
# NumPy is the foundational library for the scientific computing library for the Python ecosystem. The main libraries in the ecosystem - pandas, matplotlib, SciPy and scikit-learn are based on NumPy. Please refer to http://numpy.org for more information. 
# 
# As it is a foundational library, it is important to know at least the basics of NumPy. 

# ### NumPy Tutorial
# Here are a couple of motivating examples about why vectorization is needed when doing any kind of scientific computing. Let's perform a couple of simple calculations with Python. 
# 
# First, let's say you have some distances and times and you would like to calculate the speeds: 

# In[1]:


distances = [10, 15, 17, 26] 
times = [0.3, 0.47, 0.55, 1.20]

# Calculate speeds with Python
speeds = []
for i in range (len(distances)):
    speeds.append(distances[i]/times[i])
    
speeds


# An alternative to accomplish the same in Python methodology would be the following: 

# In[2]:


# An alternative
speeds = [d/t for d,t in zip(distances, times)]
speeds


# For the second motivating example, let's say you have a list of product qualities and their respective prices, and you would like to calculate the total of the purchase. The code in Python would look something like this: 

# In[3]:


product_quantities = [13, 5, 6, 10, 11]
prices = [1.2, 6.5, 1.0, 4.8, 5.0]
total = sum([q*p for q,p in zip(product_quantities, prices)])
total


# The point of these two examples is that, for this type of calculation, you need to perform operations element by element and in Python (as well as most programming languages) you do it by using for loops or list comprehensions (which are just convenient ways of writing for loops). 
# 
# Vectorization is a style of computer programming where operations are applied to arrays of individual elements. In other words, a vectorized operation is the application of the operation, element by element, without explicitly doing it with for loops. 
# 
# Now, let's take a look at the NumPy approach to doing the former operations: 
# 
# First, let's import the NumPy library and do the speeds calculation. As you can see, by adding the mathematical definition of speed, this is very easy and natural.

# In[4]:


import numpy as np 

# Calculate the speeds
distances = np.array([10, 15, 17, 26]) 
times = np.array([0.3, 0.47, 0.55, 1.20])
speeds = distances/times
speeds


# Again for the purchase calculation, the code for running this calculation is much easier and more natural: 

# In[5]:


# Calculate the total of a purchase
product_quantities = np.array([13, 5, 6, 10, 11])
prices = np.array([1.2, 6.5, 1.0, 4.8, 5.0])
total = (product_quantities*prices).sum()
total


# After running this calculation, you will get the same total: 157.1
# 
# Now, let learn some of the basics of array creation, main attributes, and operations. This is of course by no means a complete introduction, but it will be enough for you to have a basic understanding of how NumPy arrays work. 
# 
# As you saw earlier, we can create arrays from lists like: 

# In[6]:


# Array from lists
product_quantities = [13, 5, 6, 10, 11]
prices = [1.2, 6.5, 1.0, 4.8, 5.0]
product_quantities = np.array(product_quantities)
prices = np.array(prices)


# If you pass a list of lists to np.array(), it will create a two-dimensional array. If passed a list of lists (three nested lists), it will create a three-dimensional array, and so on and so forth:

# In[7]:


A = np.array([[1, 2], [3, 4]])
A


# Take a look at some of the array's main attributes. Let's create some arrays containing randomly generated numbers: 

# In[8]:


# Set a seed for reproducibility 
np.random.seed(0) 

# 1D array
x1 = np.random.randint(low=0, high=9, size=12)

# 2D array
x2 = np.random.randint(low=0, high=9, size=(3, 4))

# 3D array
x3 = np.random.randint(low=0, high=9, size=(3, 4, 5))

print (x1, '\n')
print (x2, '\n')
print (x3, '\n')


# Some important array attributes are as follows: 
# 
# - ndarray.ndim: The number of dimensions (axes) of the array.
# - ndarray.shape: The dimensions of the array. This tuple of integers indicates the size of the array in each dimension.
# - ndarray.size: The total number of elements of the array. This is equal to the product of the elements of shape.
# - ndarray.dtype: An object describing the type of the elements in the array. One can create or specify dtype's using standard Python types. Moreover, NumPy provides types of its own. numpy.int32, numpy.int16 and numpy.float64 are some examples: 

# In[9]:


print ("x3 ndim: ", x3.ndim)
print ("x3 shape: ", x3.shape)
print ("x3 size: ", x3.size)
print ("x3 dtype: ", x3.dtype)


# One-dimensional arrays can be indexed, sliced and iterated over, just like lists or other Python sequences: 

# In[10]:


print (x1)
print (x1[5])   # Element at index 5
print (x1[2:5]) # Slice from elements in indexes 2, 3 & 4
print (x1[-1])  # Last element of the array


# Multi-dimensional arrays have one index per axis. These indices are given in a tuple separated by commas: 

# In[11]:


# Integers from 1 to 20 
one_to_twenty = np.arange(1, 21)

# Transform to 5-row by 4-column matrix 
my_matrix = one_to_twenty.reshape(5, 4) 

my_matrix


# In[12]:


# Display element in row 3, column 4 (remember Python is zero indexed) 
my_matrix[2, 3]


# In[13]:


# Display row in the 2nd column of my_matrix 
my_matrix[:, 1]


# In[14]:


# Display 1st & 2nd row of the last column
my_matrix[0:2, -1]


# In[15]:


# Set the 1st element to -1
my_matrix[0, 0] = -1
my_matrix


# Finally, let's perform some mathematical operations on the former matrix, just to have some examples of how vectorization works:

# In[16]:


# Integers from 1 to 20 
one_to_twenty = np.arange(1, 21)

# Tranform to 5-row by 4-column matrix 
my_matrix = one_to_twenty.reshape(5, 4) 

# The following operations are done to every element of the matrix

# Addition
my_matrix + 5


# In[17]:


# Write your Python Code to display the result of the 2D matrix subtract by 15
my_matrix - 15




# In[6]:


# Write your Python Code to display the result of the 2D matrix multiply by 8
my_matrix * 8




# In[18]:


# Division by 2
my_matrix / 2


# In[19]:


# Exponentiation (e.g. X^2)
my_matrix ** 2


# In[20]:


# Powers of 2 (e.g. 2^X)
2 ** my_matrix


# In[21]:


# Trigonometry functions like sine
np.sin(my_matrix)


# In[7]:


# Write your Python Code to display the result of the 2D matrix using hyperbolic tangent function
np.tanh(my_matrix)




# Finally, let's take a look at some useful methods commonly used in data analysis: 

# In[22]:


# Summation of all elements in the matrix
print ("Sum:", my_matrix.sum())

# Find the maximum element 
print ("Max: ", my_matrix.max()) 

# Find the minimum element 
print ("Min: ", my_matrix.min())

# Find the arithmetric mean 
print ("Mean: ", my_matrix.mean())

# Find the standard deviation 
print ("std: ", my_matrix.std())


# To learn more about Numpy, you may refer to the official quick start tutorial available at https://docs.scipy.org/doc/numpy/user/quickstart.html 

# ***
# ### Pandas
# 
# Pandas was fundamentally created for working with two types of data structures. For one-dimensional data, you have a Series. The most common use of Pandas is the two-dimensional structure called the DataFrame. You can think of it as an Excel spreadsheet or an SQL table.
# 
# ![pandas-2.png](attachment:pandas-2.png)
# 
# Although there are other data structures, with these two we can cover more than 90% of the use cases in AI. In fact, most of the time you will be working with DataFrames. If you are totally new to this library, you are recommended the 10 minutes to Pandas tutorial available at https://pandas.pydata.org/pandas-docs/stable/10min.html. 

# ***
# ### Matplotlib
# 
# This is the main library for producing 2D visualizations. It is one of the oldest scientific computing tools in the Python ecosystem. Although there is an increasing number of libraries for visualization for Python, Matplotlib is still widely used and actually incorporated into the pandas functionality. In addition, other more specialized visualization projects such as Seaborn are based on Matplotlib. You may refer to https://matplotlib/org/ for additional information. 
# 
# You will need to be familar with some of the basic terminology and concepts of matplotlib because frequently you will need to make modifications to the objects and plots produced by your Python code. Let's introduce some of the basics you need to know about this library so that you can get started visualizing data. Let's import the library as is customary when working in analytics: 

# In[38]:


import matplotlib.pyplot as plt
import os
# get_ipython().run_line_magic('matplotlib', 'inline')  # Only needed in Jupyter notebooks

# Get the directory where this script is located to save plots
# Modified to save all generated graphics as PNG files in the same directory as this script
script_dir = os.path.dirname(os.path.abspath(__file__))


# This is necessary for showing the figures in the Jupyter Notebook. 
# 
# First, you have two important objects - figures subplots (also known as axes). The diagram is the top-level container for all plot elements and is the container of subplot. One diagram can have many subplots and each subplot belongs to a single diagram. The following code produces a diagram (which is not seen) with a single empty subplot. Each subplot has many elements such as a x-axis, y-axis and a title: 

# In[39]:


fig, ax = plt.subplots()
ax.plot()
# Save the plot to the same directory as the script
plt.savefig(os.path.join(script_dir, 'empty_subplot.png'), dpi=300, bbox_inches='tight')
plt.close()  # Close the figure to free memory


# A diagram with four subplots would be produced by the following code:

# In[40]:


fig, ax = plt.subplots(ncols=2, nrows=2)
# Save the plot to the same directory as the script
plt.savefig(os.path.join(script_dir, 'four_empty_subplots.png'), dpi=300, bbox_inches='tight')
plt.close()  # Close the figure to free memory


# One important thing to know about matplotlib is that it can be confusing for the beginner because there are two ways (interfaces) of using it - Pyplot and the Object Oriented Interface (OOI). OOI is prefered because it makes explicit the object you are working with. The formerly produced axes object is a NumPy array containing the four subplots. Let's plot some random numbers just to show you how you can refer to each of the subplots. 

# In[41]:


fig, axes = plt.subplots(ncols=2, nrows=2)
axes[0, 0].set_title('upper left') 
axes[0, 0].plot(np.arange(10), np.random.randint(0, 10, 10))

axes[0, 1].set_title('upper right') 
axes[0, 1].plot(np.arange(10), np.random.randint(0, 10, 10))

axes[1, 0].set_title('lower left') 
axes[1, 0].plot(np.arange(10), np.random.randint(0, 10, 10))

axes[1, 1].set_title('lower right') 
axes[1, 1].plot(np.arange(10), np.random.randint(0, 10, 10))

# This statement is to get a nice spacing between the subplot
fig.tight_layout();
# Save the plot to the same directory as the script
plt.savefig(os.path.join(script_dir, 'four_subplots_with_random_data.png'), dpi=300, bbox_inches='tight')
plt.close()  # Close the figure to free memory


# Since the axes object is a NumPy array, you refer to each of the subplots using the NumPy indexation, then you use methods such as .set_title() or .plot() on each subplot to modify it as we would like. There are many of those methods and most of them are used to modify elements of a subplot. For example, the following is almost the same code as before, but written in a way that is a bit more compact and modified the y-axis's tick marks. 
# 
# The other API, pyploy, is the one you will find in most of the online examples. This is the code to produce the above plots using pyplot: 

# In[42]:


titles = ['upper left', 'upper right', 'lower left', 'lower right']
fig, axes = plt.subplots(ncols=2, nrows=2)
for title, ax in zip(titles, axes.flatten()):
    ax.set_title(title)
    ax.plot(np.arange(10), np.random.randint(0, 10, 10))
    ax.set_yticks([0, 5, 10])
fig.tight_layout();
# Save the plot to the same directory as the script
plt.savefig(os.path.join(script_dir, 'four_subplots_compact_with_yticks.png'), dpi=300, bbox_inches='tight')
plt.close()  # Close the figure to free memory


# The following code is a minimal example of pyplot:

# In[37]:


# Fill the ? with any numerical value between 0 to 9
plt.plot([1, 4, 7, 3])
plt.title('Minimal pyplot example')
plt.ylabel('some numbers')
# Save the plot to the same directory as the script
plt.savefig(os.path.join(script_dir, 'minimal_pyplot_example.png'), dpi=300, bbox_inches='tight')
plt.close()  # Close the figure to free memory


# ***
'''
To run this codes:
python "Lesson08 - Machine Learning\Lab Tutorials py files\8.1_Using_NumPy.py"

'''