#!/usr/bin/env python
# coding: utf-8

"""
Verification script to confirm that all graphics generated by 9.4_Decision_Tree.py
are saved in the same directory as the script.
"""

import os

def verify_graphics_location():
    """Verify that all graphics are saved in the same directory as the script."""
    
    # Get the directory where this script is located
    script_dir = os.path.dirname(os.path.abspath(__file__))
    print(f"Script directory: {script_dir}")
    print("=" * 60)
    
    # List of graphics files that should be generated by 9.4_Decision_Tree.py
    expected_graphics = [
        "decision_tree_depth3.png",
        "decision_tree_depth3_proportions.png", 
        "feature_importance.png",
        "confusion_matrix.png"
    ]
    
    print("Checking for graphics files in the script directory:")
    print("-" * 60)
    
    all_found = True
    for graphic_file in expected_graphics:
        file_path = os.path.join(script_dir, graphic_file)
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path)
            print(f"✅ {graphic_file} - Found ({file_size:,} bytes)")
        else:
            print(f"❌ {graphic_file} - Not found")
            all_found = False
    
    print("-" * 60)
    
    # Check for model directory and file
    model_dir = os.path.join(script_dir, "model")
    model_file = os.path.join(model_dir, "dt.mdl")
    
    if os.path.exists(model_file):
        model_size = os.path.getsize(model_file)
        print(f"✅ model/dt.mdl - Found ({model_size:,} bytes)")
    else:
        print(f"❌ model/dt.mdl - Not found")
        all_found = False
    
    print("=" * 60)
    
    if all_found:
        print("🎉 SUCCESS: All graphics and model files are saved in the script directory!")
        print(f"📁 Location: {script_dir}")
    else:
        print("⚠️  WARNING: Some files are missing. Run 9.4_Decision_Tree.py first.")
    
    print("\nTo generate the graphics, run:")
    print('python "9.4_Decision_Tree.py"')
    
    return all_found

if __name__ == "__main__":
    verify_graphics_location()
